import React, { useCallback, useEffect } from "react";
import {
	VideoPreviewProps,
	PostMessageAccount,
	PostMessageObject,
	VideoPropsProduct,
	PostMessageVideo,
	PostMessageProduct
} from "@src/types/snippetOptions";
import styled, { css } from "styled-components";

const Iframe = styled.iframe<{ displayMode: "landscape" | "portrait" }>`
	width: "auto";
	height: 500px;

	${({ displayMode }) =>
		displayMode === "landscape"
			? css`
					aspect-ratio: 16 / 9;
					
					@media (max-width: 1110px) {
						aspect-ratio: 9 / 16;
					}
			  `
			: css`
					aspect-ratio: 9 / 16;
			  `}
`;

const iframeId = "video-preview-iframe";

const getVideoPreviewIframe = (): HTMLIFrameElement => {
	const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
	if (!iframe) {
		throw new Error("Video preview iframe not found");
	}

	return iframe;
};

const sendPostMessage = (message: string) => {
	const iframe = getVideoPreviewIframe();

	if (!iframe.contentWindow) {
		throw new Error("Video preview iframe content window not found");
	}

	iframe.contentWindow.postMessage(message, "*");
};

const encodeMessageData = (messageObject: object): string => {
	const messageString = JSON.stringify(messageObject);
	return "!gpapp:" + messageString;
};

const createPostMessage = (account: PostMessageAccount, video: PostMessageVideo): string => {
	const messageObject: PostMessageObject = {
		type: "render_video_preview",
		data: {
			video: video,
			account: account
		}
	};

	return encodeMessageData(messageObject);
};

const propsProductsToVideoProducts = (products: VideoPropsProduct[]): PostMessageProduct[] => {
	const filtered = products.filter((product) => {
		return product.productTitle && product.productURL;
	});

	return filtered.map((product) => {
		return {
			title: product.productTitle,
			url: product.productURL,
			productThumbnail: product.imagePreview,
			subTitle: product.subTitle
		};
	});
};

const createVideoDataFromProps = (props: VideoPreviewProps) => {
	const products = propsProductsToVideoProducts(props.products);

	return {
		title: props.title,
		videoURL: props.videoURL,
		videoPosterURL: props.videoPosterURL,
		ctaText: "Shop Now",
		showTitle: props.showTitle,
		products: products,
		videoDisplayMode: props.videoDisplayMode,
		email: props?.emailAddress,
		phone: props?.phoneNumber,
		captionData: props.captionData
	};
};

const updateVideoPreview = (props: VideoPreviewProps) => {
	try {
		const accountData = {
			allowLandscape: props?.subscription?.allowLandscape,
			allowThemes: props?.subscription?.allowThemes,
			allowSharing: props?.subscription?.allowSharing,
			hideVanityBranding: props?.subscription?.hideVanityBranding,
			subscriptionType: props?.subscription?.type,
			allowCTALead: props?.subscription?.allowCTALead
		};

		const videoData = createVideoDataFromProps(props);
		sendPostMessage(createPostMessage(accountData, videoData));
	} catch (error) {
		console.error("Video preview error", error);
	}
};

const VideoPreview: React.FC<VideoPreviewProps> = (props: VideoPreviewProps) => {
	const messageListener = useCallback(
		(event: MessageEvent) => {
			if (event.data === "gp_preview_initialized") {
				updateVideoPreview(props);
				window.removeEventListener("message", messageListener);
			}
		},
		[props]
	);

	useEffect(() => {
		updateVideoPreview(props);
	}, [props]);

	useEffect(() => {
		window.addEventListener("message", messageListener);

		return () => {
			window.removeEventListener("message", messageListener);
		};
	});

	return (
		<Iframe
			id={iframeId}
			displayMode={props.videoDisplayMode === "landscape" ? "landscape" : "portrait"}
			src={`${props.appEndpoint}/?previewMode=true`}
		/>
	);
};

export default VideoPreview;

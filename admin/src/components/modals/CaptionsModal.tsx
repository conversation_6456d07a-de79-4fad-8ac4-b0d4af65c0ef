import React, { useRef, useEffect, useState, RefObject, useCallback } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { BodyText, XmarkIcon } from "@src/styles/forms";
import { Form, Row, Col } from "react-bootstrap";
import {
	Flex,
	ProgressBar,
	ProgressBarTime,
	CanvasImage,
	VideoSectionBox,
	VideoPlayer,
	MainButton,
	LinkButton,
	CaptionsSectionModal,
	CaptionsRightColumn,
	CaptionsBox,
	EditCaptionsArea,
	ColorBoxContainer,
	ColorBox,
	ColorInput
} from "@src/styles/components";
import { shoppableVideoData } from "@src/types/videos";
import { CaptionData } from "@src/types/caption";
import { useMediaQuery } from "react-responsive";
import { DisplayFormatOptions } from "@src/types/snippetOptions";
import { HexAlphaColorPicker } from "react-colorful";

interface Props {
	displayFormat: DisplayFormatOptions;
	visible: boolean;
	onCancel: () => void;
	uploadFileURL?: string;
	captionData?: CaptionData;
	setCaptionData: (value: CaptionData) => void;
	videoDataCopy?: shoppableVideoData;
	setVideoDataCopy?: (value: shoppableVideoData) => void;
	setEditVideoError: (value: string) => void;
}

// eslint-disable-next-line max-lines-per-function
export const CaptionsModal: React.FC<Props> = ({
	displayFormat,
	visible,
	onCancel,
	uploadFileURL,
	captionData,
	setCaptionData,
	videoDataCopy,
	setVideoDataCopy,
	setEditVideoError
}) => {
	const translation = useTranslation();
	const [videoCurrentTime, setVideoCurrentTime] = useState("0:00");
	const [videoDuration, setVideoDuration] = useState("0:00");
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });
	const [isPickerVisible, setPickerVisible] = useState(false);
	const [isTextColorVisible, setIsTextColorVisible] = useState(false);
	const [activeCaptionIndex, setActiveCaptionIndex] = useState<number | null>(null);
	const [captionDataCopy, setCaptionDataCopy] = useState<CaptionData | undefined>();

	const captureImage = useCallback(() => {
		const canvas = document.getElementById("canvasImage") as HTMLCanvasElement;
		const video = document.getElementById("videoForImage") as HTMLVideoElement;
		let progressPercentage = (video.currentTime / video.duration) * 100;

		if (progressPercentage < 0) {
			progressPercentage = 0;
		}
		if (progressPercentage > 100) {
			progressPercentage = 100;
		}
		canvas.style.left = `${progressPercentage}%`;

		const context = canvas.getContext("2d");
		if (!context) {
			setEditVideoError(translation.errors.canvas2D);
			return;
		}
		canvas.width = video.videoWidth;
		canvas.height = video.videoHeight;
		context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
	}, [translation.errors.canvas2D, setEditVideoError]);

	const secondsToMinute = (seconds: number) => {
		let s = Math.round(seconds);
		return (s - (s %= 60)) / 60 + (9 < s ? ":" : ":0") + s;
	};

	const seekTo = (moveX: number) => {
		const video = document.getElementById("videoForImage") as HTMLVideoElement;
		const progressBar = document.querySelector(".customProgressBar");

		if (progressBar) {
			const progressBarRect = progressBar.getBoundingClientRect();
			const clickPosition = moveX - progressBarRect.left;
			const progressBarWidth = progressBarRect.width;
			const progressPercentage = clickPosition / progressBarWidth;

			let targetSecond = video.duration * progressPercentage;
			targetSecond = Math.max(0, Math.min(targetSecond, video.duration));

			video.currentTime = targetSecond;
			setVideoCurrentTime(secondsToMinute(targetSecond));
			captureImage();
		}
	};

	let isDragging = false;

	// touch event
	const touchSeek = (moveX: number) => {
		isDragging = true;
		// Update progress when starting to drag
		seekTo(moveX);
	};

	document.addEventListener("touchmove", (event: TouchEvent) => {
		if (isDragging) {
			seekTo(event.touches[0].clientX);
		}
	});

	document.addEventListener("touchend", () => {
		isDragging = false;
	});

	// click event
	const startSeek = (event: {clientX: number}) => {
		isDragging = true;
		// Update progress when starting to drag
		seekTo(event.clientX);
	};

	document.addEventListener("mousemove", (event: {clientX: number}) => {
		if (isDragging) {
			seekTo(event.clientX);
		}
	});

	document.addEventListener("mouseup", () => {
		isDragging = false;
	});

	const useOutsideClick = (ref: RefObject<HTMLElement>, callback: () => void) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					callback();
				}
			}
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}, [ref, callback]);
	};

	const modalRef = useRef(null);
	const bgPickerRef = useRef(null);
	const textPickerRef = useRef(null);

	useOutsideClick(modalRef, onCancel);
	useOutsideClick(bgPickerRef, () => setPickerVisible(false));
	useOutsideClick(textPickerRef, () => setIsTextColorVisible(false));


	useEffect(() => {
		if (uploadFileURL && visible) {
			const video = document.getElementById("videoForImage") as HTMLVideoElement;
			const canvas = document.getElementById("canvasImage") as HTMLCanvasElement;
			if (canvas) {
				// for initial canvas image and duration time
				video.addEventListener("loadedmetadata", () => {
					setVideoCurrentTime("0:00");
					video.pause();
					video.currentTime = 0;
					setVideoDuration(secondsToMinute(video.duration));

					setTimeout(() => {
						canvas.click();
					}, 500);
				});
			}
		}
	}, [uploadFileURL, visible, captureImage]);

	const deepCopyCaptionData = (data: CaptionData): CaptionData => ({
		...data,
		captionText: data.captionText.map(caption => ({ ...caption }))
	});

	useEffect(() => {
		if (captionData) {
			setCaptionDataCopy(deepCopyCaptionData(captionData));
		}
	}, [captionData]);

	useEffect(() => {
		if (!uploadFileURL || !visible) return;

		const video = document.getElementById("videoForImage") as HTMLVideoElement;

		const updateActiveCaption = () => {
			const time = video.currentTime;

			if (!captionDataCopy) {
				setActiveCaptionIndex(null);
				return;
			}

			const index = captionDataCopy.captionText.findIndex((caption) => time >= caption.startTime && time <= caption.endTime);
			setActiveCaptionIndex(index !== -1 ? index : null);
		};

		video.addEventListener("timeupdate", updateActiveCaption);
		return () => video.removeEventListener("timeupdate", updateActiveCaption);
	}, [captionDataCopy, uploadFileURL, visible]);

	const handleSaveCaptions = () => {
		if (videoDataCopy && setVideoDataCopy) {
			setVideoDataCopy({
				...videoDataCopy,
				shoppableVideo: {
					...videoDataCopy.shoppableVideo,
					captionData: captionDataCopy
				}
			});
		}
		if (captionDataCopy) {
			setCaptionData(captionDataCopy);
		}
		onCancel();
	};

	const changeCaptionsTheme = (value: string, type: string) => {
		if (!captionDataCopy) return;
		const updatedCaptions = { ...captionDataCopy };
		const safeColor = ((type === "text" || type === "background") && value.startsWith("#") && !isNaN(parseInt(value.substring(1), 16))) ? value : "#000000";

		switch (type) {
			case "text":
				updatedCaptions.textColor = safeColor;
				break;
			case "background":
				updatedCaptions.backgroundColor = safeColor;
				break;
			case "size":
				updatedCaptions.fontSizeRem = parseFloat(value);
				break;
			default:
				return;
		}

		setCaptionDataCopy(updatedCaptions);
	};

	const updateCaptionText = (index: number, text: string) => {
		if (!captionDataCopy || !captionDataCopy.captionText[index]) return;
		const updatedCaptions = deepCopyCaptionData(captionDataCopy);
		updatedCaptions.captionText[index].text = text;
		setCaptionDataCopy(updatedCaptions);
	};

	return (
		<BaseModal visible={visible} wrapperRef={modalRef} wide={true}>
			{!isScreenSmall ? (
				<LinkButton
					data-testid="closeButton"
					onClick={onCancel}
					style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}
				>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={onCancel}>
					<XmarkIcon style={{ marginTop: "-80px" }} />
				</LinkButton>
			)}
			<Row className="mt-3">
				<Col sm="12" md="8" className="mb-2">
					<CaptionsSectionModal>
						<VideoSectionBox video={true} landscape={displayFormat === DisplayFormatOptions.LANDSCAPE}>
							<VideoPlayer
								id="videoForImage"
								src={uploadFileURL}
								autoPlay={true}
								playsInline={true}
								loop={false}
								muted={true}
								crossOrigin="anonymous"
								webkit-playsinline=""
								x5-playsinline=""
							></VideoPlayer>
							<CaptionsBox
								style={{
									background: captionDataCopy?.backgroundColor,
									color: captionDataCopy?.textColor,
									fontSize: `${captionDataCopy?.fontSizeRem && displayFormat === DisplayFormatOptions.LANDSCAPE ? captionDataCopy.fontSizeRem * 0.5 : captionDataCopy?.fontSizeRem}rem`,
									padding: activeCaptionIndex !== null && captionDataCopy?.captionText[activeCaptionIndex]?.text ? "0.5rem" : "0px"
								}}
							>
								{activeCaptionIndex !== null && captionDataCopy?.captionText[activeCaptionIndex]?.text}
							</CaptionsBox>
						</VideoSectionBox>
						<Flex>
							<ProgressBar
								className="customProgressBar"
								onClick={(e) => seekTo(e.clientX)}
								onMouseDown={(e) => startSeek(e)}
								onTouchStart={(e) => touchSeek(e.touches[0].clientX)}
							>
								<CanvasImage id="canvasImage" landscape={displayFormat === DisplayFormatOptions.LANDSCAPE} />
							</ProgressBar>
						</Flex>
						<Flex style={{ marginTop: "10px", justifyContent: "space-between" }}>
							<ProgressBarTime>{videoCurrentTime}</ProgressBarTime>
							<ProgressBarTime>{videoDuration}</ProgressBarTime>
						</Flex>
					</CaptionsSectionModal>
				</Col>
				<Col sm="12" md="4" className="mb-2">
					<CaptionsRightColumn>
						<BodyText>
							<b>{videoCurrentTime}</b>
						</BodyText>

						{activeCaptionIndex !== null && captionDataCopy ? (
							<EditCaptionsArea
								value={captionDataCopy?.captionText[activeCaptionIndex].text}
								onChange={(e) => updateCaptionText(activeCaptionIndex, e.target.value)}
								rows={5}
							/>
						) : (
							<EditCaptionsArea value="" disabled={true} rows={5} />
						)}
						<>
							<div className="mt-3 mb-3" style={{ position: "relative" }}>
								{translation.modals.backgroundColor}
								<ColorBoxContainer>
									<ColorBox
										color={captionDataCopy?.backgroundColor}
										onClick={() => {
											setPickerVisible(!isPickerVisible);
										}}
									></ColorBox>
									<ColorInput
										type="text"
										value={captionDataCopy?.backgroundColor}
										onChange={(value) => {
											changeCaptionsTheme(value.target.value, "background");
										}}
										maxLength={9}
									/>
								</ColorBoxContainer>
								{isPickerVisible && (
									<div ref={bgPickerRef} style={{ position: "absolute", zIndex: 1 }}>
										<HexAlphaColorPicker
											color={captionDataCopy?.backgroundColor}
											onChange={(value) => {
												changeCaptionsTheme(value, "background");
											}}
											style={{ cursor: "pointer" }}
										/>
									</div>
								)}
							</div>

							<div className="mb-3" style={{ position: "relative" }}>
								{translation.modals.textColor}
								<ColorBoxContainer>
									<ColorBox
										color={captionDataCopy?.textColor}
										onClick={() => {
											setIsTextColorVisible(!isTextColorVisible);
										}}
									></ColorBox>
									<ColorInput
										type="text"
										value={captionDataCopy?.textColor}
										onChange={(value) => {
											changeCaptionsTheme(value.target.value, "text");
										}}
										maxLength={9}
									/>
								</ColorBoxContainer>
								{isTextColorVisible && (
									<div ref={textPickerRef} style={{ position: "absolute", zIndex: 1 }}>
										<HexAlphaColorPicker
											color={captionDataCopy?.textColor}
											onChange={(value) => {
												changeCaptionsTheme(value, "text");
											}}
											style={{ cursor: "pointer" }}
										/>
									</div>
								)}
							</div>

							<div className="mb-3">
								{translation.modals.textSize}
								<Form.Range
									value={captionDataCopy?.fontSizeRem}
									onChange={(e) => {
										changeCaptionsTheme(e.target.value, "size");
									}}
									min="0.7"
									max="2"
									step="0.1"
								/>
							</div>
						</>
						<MainButton
							type="button"
							onClick={() => handleSaveCaptions()}
							className="mx-auto mt-5"
							data-testid="setCaptions"
						>
							{translation.modals.setCaptions}
						</MainButton>
					</CaptionsRightColumn>
				</Col>
			</Row>
		</BaseModal>
	);
};

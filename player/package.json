{"name": "ap-player", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "webpack serve --mode=development", "build": "rimraf dist && webpack --mode=production", "lint": "eslint --ext .ts,.tsx src", "lint:fix": "eslint --ext .ts,.tsx src --fix", "typecheck": "tsc --noEmit", "test": "jest", "audit": "better-npm-audit audit --level=low"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.4", "@stylistic/eslint-plugin-ts": "^3.0.1", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/dotenv-webpack": "^7.0.3", "@types/jest": "^29.5.0", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/react-transition-group": "^4.4.5", "@types/styled-components": "^5.1.26", "@types/styled-system": "^5.1.16", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "babel-loader": "^9.1.2", "babel-plugin-macros": "^3.1.0", "better-npm-audit": "^3.7.3", "copy-webpack-plugin": "^11.0.0", "dotenv-webpack": "^8.0.1", "eslint": "8.56.0", "eslint-plugin-import-newlines": "^1.3.4", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "rimraf": "^6.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.3", "webpack": "^5.78.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^5.2.2"}, "dependencies": {"bson-objectid": "^2.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.0", "react-lottie-player": "^2.1.0", "react-router-dom": "^6.26.1", "react-swipeable": "^7.0.0", "recoil": "^0.7.7", "styled-components": "^5.3.9", "styled-system": "^5.1.5"}, "overrides": {"@humanwhocodes/config-array": "0.11.14", "@humanwhocodes/object-schema": "1.2.1", "rimraf": "^6.0.0"}}
import React, {
	useRef,
	useState,
	useMemo,
	useEffect
} from "react";
import styled from "styled-components/macro";
import { IVideoData } from "@player/app";

interface Caption {
	timeStamp: number;
	videoData: IVideoData;
	show: boolean;
}

const FontDiv = styled.div`
	font-family: ${(props): string => props.theme.fonts.family};
`;

export const CaptionOverlay: React.FC<Caption> = ({ timeStamp, videoData, show = false }) => {

	const xPos = videoData.captionData?.xPos;
	const yPos = videoData.captionData?.yPos;
	const textColor = videoData.captionData?.textColor;
	const backgroundColor = videoData.captionData?.backgroundColor;
	const fontSize = videoData.captionData?.fontSizeRem;
	const captionText = videoData.captionData?.captionText;

	const showTitle = useMemo(() => videoData.showTitle, [videoData.showTitle]);
	const hasProduct = useMemo(() => videoData.products.length > 0, [videoData.products.length]);

	const [position, setPosition] = useState({ x: xPos, y: yPos });
	const [offsetY, setOffsetY] = useState(40);

	const divRef = useRef<HTMLDivElement>(null);
	const fontDivRef = useRef<HTMLDivElement>(null);
	const [width, setWidth] = useState(0);
	const [height, setHeight] = useState(0);
	const [absWidth, setAbsWidth] = useState(0);

	const [textIndex, setTextIndex] = useState(0);
	const [prevTimeStamp, setPrevTimeStamp] = useState(0);
	const [text, setText] = useState("");

	const [fontsReady, setFontsReady] = useState(false);

	useEffect(() => {
		document.fonts.ready.then(() => setFontsReady(true));
	}, []);

	useEffect(() => {
		let tempTextIndex = textIndex;

		if (prevTimeStamp > timeStamp) {
			setTextIndex(0);

			//Setter doesn't update variable immediately
			tempTextIndex = 0;
		}

		let found = false;
		if (captionText) {
			for (let i = tempTextIndex; i < captionText.length; i++) {
				if (timeStamp < captionText[i].startTime)
					break;

				if (timeStamp >= captionText[i].startTime && timeStamp < captionText[i].endTime) {
					found = true;
					setText(captionText[i].text);
					if (i > tempTextIndex)
						setTextIndex(i);
					break;
				}
			}
		}

		if (!found)
			setText("");

		setPrevTimeStamp(timeStamp);
	}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	, [timeStamp]);

	useEffect(() => {
		if (fontsReady && fontDivRef.current) {
			//Ensures calculation remains consistent in the event of overflow line-breaks
			const oneLineFontDiv = fontDivRef.current.cloneNode(true) as HTMLDivElement;

			oneLineFontDiv.style.whiteSpace = "pre";
			oneLineFontDiv.style.visibility = "hidden";
			(oneLineFontDiv.children[0].children[0] as HTMLParagraphElement).style.whiteSpace = "unset";
			document.body.appendChild(oneLineFontDiv);

			const absRect = oneLineFontDiv.children[0].getBoundingClientRect();
			setAbsWidth(absRect.width);

			document.body.removeChild(oneLineFontDiv);
		}
	}, [fontsReady, text, fontSize, show, videoData.captionData?.enabled]);

	useEffect(() => {
		const progressBarHeight = 40;
		const progressBarGap = 10;
		const soloTitleHeight = 50;
		const soloProductHeight = 90;
		const titleAndProductHeight = 120;

		if (showTitle && hasProduct)
			setOffsetY(progressBarHeight + progressBarGap + titleAndProductHeight);
		else if (showTitle)
			setOffsetY(progressBarHeight + progressBarGap + soloTitleHeight);
		else if (hasProduct)
			setOffsetY(progressBarHeight + progressBarGap + soloProductHeight);
		else
			setOffsetY(progressBarHeight);

	}, [showTitle, hasProduct]);

	useEffect(() => {
		setPosition({ x: (xPos ?? 0) - (absWidth / 2), y: (yPos ?? 0) - (height / 2) });
	}, [absWidth, height, xPos, yPos]);

	// eslint-disable-next-line react-hooks/exhaustive-deps
	useEffect(() => {
		if (divRef.current) {
			const rect = divRef.current.getBoundingClientRect();
			if (width !== rect.width)
				setWidth(rect.width);
			if (height !== rect.height)
				setHeight(rect.height);
		}
	});

	if (videoData.captionData == undefined || !videoData.captionData.enabled || !show) return null;

	return <FontDiv ref={fontDivRef}>
		<div ref={divRef} style={{
			position: "absolute",
			left: `clamp(0%, calc(50% + ${position.x}px), calc(100% - ${absWidth}px))`,
			top: `clamp(0%, calc(50% + ${position.y}px), calc(100% - ${height}px - ${offsetY}px))`,
			width: "fit-content",
			height: "fit-content",
			textAlign: "center",
			display: `${(text != "") ? "block" : "none"}`,
			padding: "10px"
		}}>
			<p style={{
				borderRadius: "10px",
				backgroundColor: backgroundColor,
				color: textColor,
				fontSize: `${fontSize}rem`,
				padding: "0.5rem",
				width: "fit-content",
				margin: "0",
				marginLeft: "auto",
				marginRight: "auto",
				whiteSpace: "pre-line",
				overflowWrap: "anywhere",
				wordBreak: "normal"
			}}>
				{text}
			</p>
		</div>
	</FontDiv>;
};

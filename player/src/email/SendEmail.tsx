import React, { useState } from "react";
import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";
import { IconButton } from "@player/components/button/IconButton";
import { default as EmailIcon } from "@player/assets/icon-email.svg";
import {
	useRecoilValue,
	useSetRecoilState
} from "recoil";
import {
	isAppInPreviewModeAtom,
	isPortraitAtom,
	appSessionIdAtom,
	isUserInteractingAtom
} from "@player/app/app.state";
import {
	sliderConfigAtom,
	sliderVideoPlayerAtom
} from "@player/slider/slider.state";
import { Overlay } from "./Overlay";
import { AnimatedForm } from "./AnimatedForm";
import {
	EmailForm,
	IEmailForm
} from "./EmailForm";
import { CloseForm } from "./CloseForm";
import { EmailSentAnimation } from "./EmailSentAnimation";
import { Heading } from "@player/components/Heading";
import { ActionButton } from "@player/components/button/ActionButton";
import { sendEmail } from "./email.service";
import { sendAppEvent } from "@player/event/event.service";
import { EventNameEnum } from "@player/event/event.enum";

const EmailSent = styled(Flex)`
	flex-direction: column;
    flex-grow: 1;
    justify-content: center;
    align-items: center;
	gap: 1rem;
	padding: 1rem;
`;

interface Props {
	email?: string;
	fullWidth: boolean;
	videoId: string;
	videoTitle: string;
	groupEmail?: string;
}

const InnerFormWrapper = styled.div`
	overflow-y: auto;
	padding: 1rem;
	
	&::-webkit-scrollbar {
		width: 10px;
	}

	&::-webkit-scrollbar-track {
		margin: 10px;
		background: transparent;
		border-radius: 12px;
	}

	&::-webkit-scrollbar-thumb {
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 12px;
		border: 2px solid transparent;
		background-clip: content-box;
	}
`;

export const SendEmail: React.FC<Props> = ({ email, fullWidth, videoId, videoTitle, groupEmail }) => {
	const setIsUserInteracting = useSetRecoilState(isUserInteractingAtom);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const appSessionId = useRecoilValue(appSessionIdAtom);
	const sliderVideoPlayer = useRecoilValue(sliderVideoPlayerAtom);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const isPortrait = useRecoilValue(isPortraitAtom);
	const [isFormVisible, setFormVisible] = useState(false);
	const [isMessageSent, setMessageSent] = useState(false);
	const [animationPlayed, setAnimationPlayed] = useState(false);
	if (!email) {
		return null;
	}

	const handleOpenClick = (event: React.MouseEvent<HTMLDivElement>): void => {
		event.stopPropagation();
		setIsUserInteracting(true);
		setFormVisible(true);
		setMessageSent(false);
	};

	const handleCloseClick = (event?: React.MouseEvent<HTMLDivElement>): void => {
		event && event.stopPropagation();
		setIsUserInteracting(false);
		setFormVisible(false);
		setMessageSent(false);
	};

	const submitEmail = (data: IEmailForm): void => {
		const emailSubject = `RE: ${videoTitle} (${videoId})`;
		sendEmail(data.email, email, data.message, emailSubject);

		if (groupEmail != undefined && groupEmail != "")
			sendEmail(data.email, groupEmail, data.message, emailSubject);

		sendAppEvent({
			collectionId: sliderConfig.collectionId,
			accountId: sliderConfig.accountId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.FEATURED_EMAIL_SUBMIT,
			videoId: videoId,
			sliderVideoPlayer: sliderVideoPlayer
		}, isAppInPreviewMode);
		setMessageSent(true);
		setAnimationPlayed(false);
	};

	return (
		<>
			<Flex alignSelf={"flex-end"}>
				<IconButton
					svgIcon={EmailIcon}
					style={{ padding: "7px" }}
					isPortrait={isPortrait}
					onClick={(e): void => handleOpenClick(e)}
				/>
			</Flex>

			{isFormVisible && <Overlay onClick={handleCloseClick} />}
			<AnimatedForm
				isVisible={isFormVisible} fullWidth={fullWidth}>
				<CloseForm onClick={handleCloseClick} isPortrait={isPortrait} />
				{!isMessageSent && (
					<InnerFormWrapper>
						<Flex justifyContent="center">
							<Heading
								style={{ color: "black", fontSize: "clamp(0.75rem, 3cqh, 1.17rem)" }}
								text={"Send a Message"}
								tag="h3"
							/>
						</Flex>
						<EmailForm onSubmit={submitEmail} />
					</InnerFormWrapper>
				)}

				{isMessageSent && (
					<EmailSent>
						{!animationPlayed ? (
							<EmailSentAnimation onAnimationComplete={(): void => setAnimationPlayed(true)} />
						) : (
							<>
								<Heading style={{ color: "black" }} text="Message Sent!" tag="h3" />
								<ActionButton variantBtn="secondary" text="Close"
									fullWidth onClicked={handleCloseClick} />
							</>
						)}
					</EmailSent>
				)}
			</AnimatedForm>
		</>
	);
};

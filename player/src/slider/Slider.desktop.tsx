import React, {
	useEffect,
	useState,
	useRef
} from "react";
import { useRecoilValue } from "recoil";
import { Box } from "@player/components";
import { IconButton } from "@player/components/button/IconButton";
import {
	SliderExit,
	SliderContainer,
	VideoWrapper
} from "./SliderContainer";
import {
	appSessionIdAtom,
	isAppInPreviewModeAtom
} from "@player/app/app.state";
import { getVideosToRender } from "@player/app/app.util";
import {
	VideoDisplayModeEnum,
	IVideoData
} from "@player/app/app.interface";
import {
	EventNameEnum,
	sendAppEvent
} from "@player/event";
import { LayoutPosEnum } from "./slider.enum";
import {
	sliderVideoPlayerAtom,
	sliderConfigAtom
} from "./slider.state";
import { VideoFrame } from "@player/interactive";
import { notifyCoreToCloseApp } from "@player/app/app.message";
import { default as TimesIcon } from "@player/assets/icon-times.svg";
interface Props {
	videoData: IVideoData[];
	starterVideo: number;
}

export const DeskLayout: React.FC<Props> = ({
	videoData,
	starterVideo
}) => {
	const appSessionId = useRecoilValue(appSessionIdAtom);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const sliderVideoPlayer = useRecoilValue(sliderVideoPlayerAtom);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);
	const containerRef = useRef<HTMLDivElement>(null);
	const middleItemRef = useRef<HTMLDivElement>(null);

	const initialVideoIndex = videoData.findIndex(
		video => sliderVideoPlayer && video._id === sliderVideoPlayer.videoId
	);
	const [currentVideo, setCurrentVideo] =
	useState<number>(initialVideoIndex !== -1 ? initialVideoIndex : starterVideo);
	const [playingAudio, setPlayingAudio] = useState<boolean>(false);

	const [videosToRender, setVideosToRender] = useState<number[]>(
		getVideosToRender(videoData.length, starterVideo, true)
	);
	const [showVideoOverlay, setShowVideoOverlay] = useState<boolean>(false);

	const handleChevronClick = (pos: LayoutPosEnum): void => {
		sendAppEvent({
			accountId: sliderConfig.accountId,
			collectionId: sliderConfig.collectionId,
			appSessionId: appSessionId,
			eventName: EventNameEnum.VIDEO_PLAY_POSITION,
			sliderVideoPlayer: sliderVideoPlayer,
			endedMethod: pos === LayoutPosEnum.RIGHT ? "next" : "previous",
			videoPlayStatus: "stopped",
			isShareMode: sliderConfig.isShareMode
		}, isAppInPreviewMode);

		if (pos === LayoutPosEnum.RIGHT) {
			setCurrentVideo((prevIndex) =>
				prevIndex === videoData.length - 1 ? 0 : prevIndex + 1
			);
		} else if (pos === LayoutPosEnum.LEFT) {
			setCurrentVideo((prevIndex) =>
				prevIndex === 0 ? videoData.length - 1 : prevIndex - 1
			);
		} else {
			console.error(
				"handleChevronClick is called on undefined position. pos=",
				pos
			);
		}
	};

	const centerMiddleItem = (): void => {
		if (containerRef.current && middleItemRef.current) {
			const viewportWidth = window.innerWidth;
			const middleItemWidth = middleItemRef.current.clientWidth;
			const middleItemOffset = middleItemRef.current.offsetLeft;
			const scrollPosition = middleItemOffset - (viewportWidth / 2) + (middleItemWidth / 2);
			containerRef.current.scrollTo({
				left: scrollPosition,
				behavior: "auto"
			});
		}
	};

	useEffect(() => {
		centerMiddleItem();
	}, [videosToRender]);

	useEffect(() => {
		window.addEventListener("resize", centerMiddleItem);
		return () => {
			window.removeEventListener("resize", centerMiddleItem);
		};
	}, []);

	useEffect(() => {
		setVideosToRender(
			getVideosToRender(videoData.length, currentVideo, true)
		);
	}, [currentVideo, videoData.length]);

	const renderVideoFrame = (idx: number, position: LayoutPosEnum): JSX.Element | null => {
		const index = videosToRender.length === 1 ? 0 : idx;
		if (videosToRender.length === 1 && position !== LayoutPosEnum.CENTER) {
			return null;
		}

		const isLandscape = videoData[videosToRender[index]]?.videoDisplayMode === VideoDisplayModeEnum.LANDSCAPE;

		return (
			<VideoWrapper
				key={"SV-Desk-key" + position}
				ref={position === LayoutPosEnum.CENTER ? middleItemRef : undefined}
				isLandscape={isLandscape}>
				<VideoFrame
					isViewable={position === LayoutPosEnum.CENTER}
					position={position}
					videoData={videoData[videosToRender[index]]}
					playingAudio={playingAudio}
					setPlayingAudio={setPlayingAudio}
					setShowVideoOverlay={setShowVideoOverlay}
					showVideoOverlay={showVideoOverlay}
					handleChevronClick={handleChevronClick}
				/>
			</VideoWrapper>
		);
	};

	const handleExitClick = (): void => {
		notifyCoreToCloseApp({
			videoPlayer: sliderVideoPlayer,
			sliderConfig: sliderConfig,
			appSessionId: appSessionId
		});
	};

	return (
		<>
			{!sliderConfig.isShareMode && <SliderExit onClick={handleExitClick}>
				<IconButton svgIcon={TimesIcon} isPortrait={false}/>
			</SliderExit>}

			<SliderContainer
				ref={containerRef}
				data-testid="desk-layout"
				style={{ cursor: "unset" }}
				onClick={handleExitClick}
			>
				<Box minWidth={"1000px"} minHeight={"2px"}></Box>
				{renderVideoFrame(0, LayoutPosEnum.LEFT)}
				{renderVideoFrame(1, LayoutPosEnum.CENTER)}
				{renderVideoFrame(2, LayoutPosEnum.RIGHT)}
				<Box minWidth={"1000px"} minHeight={"2px"}></Box>
			</SliderContainer>
		</>

	);
};

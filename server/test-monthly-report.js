// Simple test script to verify the monthly report endpoint works
const path = require('path');
const ejs = require('ejs');

async function testMonthlyReport() {
    try {
        // Test data similar to what the controller generates
        const reportData = {
            reportPeriod: "June 2025",
            monthRange: "June 2025",
            metrics: {
                impressions: { value: "104,402", change: 1034 },
                plays: { value: "3,312", change: 230 },
                clicks: { value: "426", change: -230 },
                emails: { value: "125", change: 30 },
                calls: { value: "32", change: 10 },
                engaged: { value: "2,045", change: 54 },
                playtime: { value: "44h 35m 16s", change: "1h 32m 10s" },
                likes: { value: "45", change: 23 }
            },
            dailyPlaytimeData: {
                labels: ["6/1", "6/2", "6/3", "6/4", "6/5", "6/6", "6/7"],
                values: [5.5, 3.2, 6.1, 7.3, 7.8, 2.8, 8.1]
            },
            dailyPerformanceData: {
                labels: ["6/1", "6/2", "6/3", "6/4", "6/5", "6/6", "6/7"],
                emails: [11, 9, 13, 15, 16, 8, 17],
                calls: [4, 6, 8, 6, 5, 9, 9],
                clicks: [15, 14, 12, 16, 12, 11, 14],
                likes: [7, 5, 5, 9, 8, 5, 11]
            },
            dailyPlaysData: {
                currentWeek: [5800, 7200, 6200, 5600, 4800, 5400, 6600],
                previousWeek: [4000, 6400, 4800, 6200, 3600, 4000, 7200]
            },
            metricsData: {
                engagementRate: { value: "4.23%", change: 1.20 },
                playRate: { value: "1.21%", change: 0.31 },
                clickthroughRate: { value: "4.53%", change: -0.42 },
                leadCount: { value: "4,502", change: 125 },
                playsPerSession: { value: "10", change: 2 },
                avgPlaytime: { value: "4:02", change: "4:32" }
            },
            videos: [
                {
                    position: 1,
                    change: 'up',
                    changeValue: 1,
                    title: 'Video 1',
                    length: '1:45',
                    plays: 202,
                    clicks: 30,
                    emails: 12,
                    calls: 12,
                    playtime: '2:30',
                    score: 94
                }
            ],
            insights: [
                "Your content performed exceptionally well in June 2025.",
                "The engagement rate shows strong audience interest."
            ]
        };

        const templatePath = path.resolve(__dirname, 'src/assets/templates/reports/monthly-report.ejs');
        console.log('Template path:', templatePath);
        
        const html = await ejs.renderFile(templatePath, reportData);
        console.log('✅ Template rendered successfully!');
        console.log('HTML length:', html.length, 'characters');
        
        // Write to file for inspection
        const fs = require('fs');
        fs.writeFileSync('test-output.html', html);
        console.log('📄 Output written to test-output.html');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error(error.stack);
    }
}

testMonthlyReport();
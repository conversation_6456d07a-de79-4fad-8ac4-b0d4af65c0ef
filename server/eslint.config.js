import js from "@eslint/js";
import tseslint from "@typescript-eslint/eslint-plugin";
import tsparser from "@typescript-eslint/parser";
import stylistic from "@stylistic/eslint-plugin-ts";
import importNewlines from "eslint-plugin-import-newlines";
import globals from "globals";

export default [
	js.configs.recommended,
	{
		ignores: ["node_modules/", "dist/"]
	},
	{
		files: ["**/*.ts", "**/*.js"],
		languageOptions: {
			parser: tsparser,
			parserOptions: {
				ecmaVersion: "latest",
				sourceType: "module"
			},
			globals: {
				...globals.node,
				...globals.es2021,
				...globals.jest,
				Express: "readonly",
				NodeJS: "readonly"
			}
		},
		plugins: {
			"@typescript-eslint": tseslint,
			"import-newlines": importNewlines,
			"@stylistic/ts": stylistic
		},
		rules: {
			"@typescript-eslint/no-explicit-any": "off",
			"@typescript-eslint/explicit-function-return-type": "error",
			"@typescript-eslint/no-unused-vars": "warn",
			"@stylistic/ts/indent": ["error", "tab"],
			"linebreak-style": ["error", "unix"],
			"semi": ["error", "always"],
			"quotes": ["error", "double"],
			"no-trailing-spaces": "error",
			"comma-dangle": ["error", "never"],
			"no-mixed-spaces-and-tabs": "error",
			"space-in-parens": ["error", "never"],
			"max-len": ["error", { "code": 120 }],
			"max-lines": ["error", { "max": 1000, "skipBlankLines": true, "skipComments": true }],
			"max-depth": ["error", 4],
			"max-lines-per-function": ["error", { "max": 200, "skipBlankLines": true, "skipComments": true }],
			"max-params": ["error", 4],
			"no-console": ["warn", { "allow": ["info", "error"] }],
			"no-unexpected-multiline": "error",
			"no-unreachable": "warn",
			"no-irregular-whitespace": "error",
			"no-fallthrough": "error",
			"no-duplicate-case": "error",
			"no-duplicate-imports": "error",
			"no-await-in-loop": "error",
			"camelcase": "error",
			"no-else-return": "error",
			"no-var": "error",
			"no-inline-comments": "error",
			"no-constant-condition": "warn",
			"no-undef": "warn",
			"no-nested-ternary": "error",
			"no-useless-catch": "error",
			"no-useless-return": "error",
			"comma-spacing": ["error", { "before": false, "after": true }],
			"comma-style": ["error", "last"],
			"dot-location": ["error", "property"],
			"eol-last": ["error", "always"],
			"func-call-spacing": ["error", "never"],
			"key-spacing": ["error", { "beforeColon": false, "afterColon": true }],
			"keyword-spacing": ["error", { "before": true, "after": true }],
			"function-call-argument-newline": ["error", "consistent"],
			"max-statements-per-line": ["error", { "max": 1 }],
			"no-multi-spaces": "error",
			"semi-style": ["error", "last"],
			"no-warning-comments": ["warn", { "terms": ["todo", "fixme"], "location": "start" }],
			"import-newlines/enforce": ["error", { "items": 1, "max-len": 120, "semi": true, "forceSingleLine": true }],
			"space-infix-ops": ["error", { "int32Hint": false }]
		}
	},
	{
		files: ["*.ts"],
		rules: {
			"array-bracket-spacing": ["error", "never"],
			"object-curly-spacing": ["error", "always"]
		}
	},
	{
		files: ["*.js"],
		rules: {
			"@typescript-eslint/explicit-function-return-type": "off",
			"array-bracket-spacing": ["error", "never"],
			"object-curly-spacing": ["error", "always"]
		}
	}
];

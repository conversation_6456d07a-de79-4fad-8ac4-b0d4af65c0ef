import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { APIError } from '../../utils/helpers/apiError';
import { APIErrorName } from '../../interfaces/apiTypes';

export interface PDFOptions {
    format?: 'A4' | 'A3' | 'Letter';
    orientation?: 'portrait' | 'landscape';
    margin?: {
        top?: string;
        right?: string;
        bottom?: string;
        left?: string;
    };
    displayHeaderFooter?: boolean;
    headerTemplate?: string;
    footerTemplate?: string;
    printBackground?: boolean;
}

export class PDFService {
    private browser: Browser | null = null;

    /**
     * Initialize the PDF service with a browser instance
     */
    public async initialize(): Promise<void> {
        try {
            // More robust browser launch configuration
            this.browser = await puppeteer.launch({
                headless: 'new', // Use new headless mode
                timeout: 30000, // 30 second timeout
                executablePath: process.env.CHROME_BIN || '/usr/bin/chromium',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-ipc-flooding-protection'
                ]
            });
        } catch (error) {
            throw new APIError(
                APIErrorName.E_SERVICE_FAILED,
                `Failed to initialize PDF service: ${(error as Error).message}`
            );
        }
    }

    /**
     * Generate PDF from HTML content
     */
    public async generatePDFFromHTML(
        htmlContent: string,
        options: PDFOptions = {}
    ): Promise<Buffer> {
        if (!this.browser) {
            await this.initialize();
        }

        let page: Page | null = null;

        try {
            page = await this.browser!.newPage();

            // Set viewport for consistent rendering
            await page.setViewport({
                width: 1200,
                height: 800,
                deviceScaleFactor: 2
            });

            // Set content and wait for it to load
            await page.setContent(htmlContent, {
                waitUntil: ['networkidle0', 'domcontentloaded']
            });

            // Wait for charts to render (if any)
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Generate PDF with options
            const pdfBuffer = await page.pdf({
                format: options.format || 'A4',
                landscape: options.orientation === 'landscape',
                margin: options.margin || {
                    top: '20mm',
                    right: '15mm',
                    bottom: '20mm',
                    left: '15mm'
                },
                displayHeaderFooter: options.displayHeaderFooter || false,
                headerTemplate: options.headerTemplate || '',
                footerTemplate: options.footerTemplate || '',
                printBackground: options.printBackground !== false, // Default to true
                preferCSSPageSize: true
            });

            return pdfBuffer;

        } catch (error) {
            throw new APIError(
                APIErrorName.E_SERVICE_FAILED,
                `Failed to generate PDF: ${(error as Error).message}`
            );
        } finally {
            if (page) {
                await page.close();
            }
        }
    }

    /**
     * Generate PDF from URL
     */
    public async generatePDFFromURL(
        url: string,
        options: PDFOptions = {}
    ): Promise<Buffer> {
        if (!this.browser) {
            await this.initialize();
        }

        let page: Page | null = null;

        try {
            page = await this.browser!.newPage();

            // Set viewport for consistent rendering
            await page.setViewport({
                width: 1200,
                height: 800,
                deviceScaleFactor: 2
            });

            // Navigate to URL and wait for it to load
            await page.goto(url, {
                waitUntil: ['networkidle0', 'domcontentloaded']
            });

            // Wait for charts to render (if any)
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Generate PDF with options
            const pdfBuffer = await page.pdf({
                format: options.format || 'A4',
                landscape: options.orientation === 'landscape',
                margin: options.margin || {
                    top: '20mm',
                    right: '15mm',
                    bottom: '20mm',
                    left: '15mm'
                },
                displayHeaderFooter: options.displayHeaderFooter || false,
                headerTemplate: options.headerTemplate || '',
                footerTemplate: options.footerTemplate || '',
                printBackground: options.printBackground !== false,
                preferCSSPageSize: true
            });

            return pdfBuffer;

        } catch (error) {
            throw new APIError(
                APIErrorName.E_SERVICE_FAILED,
                `Failed to generate PDF from URL: ${(error as Error).message}`
            );
        } finally {
            if (page) {
                await page.close();
            }
        }
    }

    /**
     * Close the browser instance
     */
    public async close(): Promise<void> {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
    }

    /**
     * Get default PDF options optimized for reports
     */
    public static getReportPDFOptions(): PDFOptions {
        return {
            format: 'A4',
            orientation: 'portrait',
            margin: {
                top: '15mm',
                right: '10mm',
                bottom: '15mm',
                left: '10mm'
            },
            displayHeaderFooter: true,
            headerTemplate: `
                <div style="font-size: 10px; margin: 0 auto; color: #666; width: 100%; text-align: center;">
                    VidVisor Performance Report
                </div>
            `,
            footerTemplate: `
                <div style="font-size: 10px; margin: 0 auto; color: #666; width: 100%; text-align: center;">
                    <span class="date"></span> | Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                </div>
            `,
            printBackground: true
        };
    }
}

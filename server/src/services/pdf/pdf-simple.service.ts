import { APIError } from '../../utils/helpers/apiError';
import { APIErrorName } from '../../interfaces/apiTypes';

/**
 * Simple PDF service that returns HTML with print-friendly CSS
 * This is a fallback when Puppeteer is not available
 */
export class SimplePDFService {
    
    /**
     * Generate a print-friendly HTML that can be converted to PDF by the browser
     */
    public static generatePrintableHTML(htmlContent: string): string {
        // Add print-specific CSS and meta tags
        const printCSS = `
            <style>
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }
                    
                    body {
                        margin: 0;
                        padding: 20px;
                        font-size: 12px;
                        line-height: 1.4;
                    }
                    
                    .report-container {
                        box-shadow: none !important;
                        border-radius: 0 !important;
                        max-width: none !important;
                        width: 100% !important;
                    }
                    
                    .chart-container {
                        page-break-inside: avoid;
                        break-inside: avoid;
                        margin-bottom: 20px;
                    }
                    
                    .metrics-grid {
                        page-break-inside: avoid;
                        break-inside: avoid;
                    }
                    
                    .video-table {
                        page-break-inside: avoid;
                        break-inside: avoid;
                        font-size: 10px;
                    }
                    
                    .ai-insights {
                        page-break-inside: avoid;
                        break-inside: avoid;
                    }
                    
                    @page {
                        margin: 15mm 10mm;
                        size: A4;
                        
                        @top-center {
                            content: "VidVisor Performance Report";
                            font-size: 10px;
                            color: #666;
                        }
                        
                        @bottom-center {
                            content: "Page " counter(page) " of " counter(pages);
                            font-size: 10px;
                            color: #666;
                        }
                    }
                }
                
                @media screen {
                    .print-instructions {
                        background: #f0f8ff;
                        border: 1px solid #4285f4;
                        border-radius: 8px;
                        padding: 15px;
                        margin: 20px 0;
                        text-align: center;
                    }
                    
                    .print-button {
                        background: #4285f4;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        margin: 0 10px;
                    }
                    
                    .print-button:hover {
                        background: #3367d6;
                    }
                }
            </style>
            
            <script>
                function printReport() {
                    window.print();
                }
                
                function savePDF() {
                    // Modern browsers support this
                    if (window.chrome) {
                        window.print();
                    } else {
                        alert('Please use Ctrl+P (Cmd+P on Mac) and select "Save as PDF" as the destination.');
                    }
                }
                
                // Auto-hide print instructions when printing
                window.addEventListener('beforeprint', function() {
                    document.querySelector('.print-instructions').style.display = 'none';
                });
                
                window.addEventListener('afterprint', function() {
                    document.querySelector('.print-instructions').style.display = 'block';
                });
            </script>
        `;
        
        // Add print instructions at the top
        const printInstructions = `
            <div class="print-instructions">
                <h3>📄 Generate PDF Report</h3>
                <p>Click the button below to print or save this report as PDF:</p>
                <button class="print-button" onclick="printReport()">🖨️ Print Report</button>
                <button class="print-button" onclick="savePDF()">💾 Save as PDF</button>
                <p><small>Or use <strong>Ctrl+P</strong> (Cmd+P on Mac) and select "Save as PDF"</small></p>
            </div>
        `;
        
        // Insert the print CSS and instructions into the HTML
        const modifiedHTML = htmlContent
            .replace('<head>', `<head>${printCSS}`)
            .replace('<div class="report-container">', `${printInstructions}<div class="report-container">`);
        
        return modifiedHTML;
    }
    
    /**
     * Generate response headers for print-friendly HTML
     */
    public static getPrintableHTMLHeaders() {
        return {
            'Content-Type': 'text/html; charset=utf-8',
            'Content-Disposition': 'inline; filename="report-printable.html"',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        };
    }
}

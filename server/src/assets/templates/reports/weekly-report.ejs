<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-bottom: 2px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #333;
        }
        
        .report-period {
            text-align: right;
            color: #666;
        }
        
        .report-period .label {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 4px;
        }
        
        .report-period .dates {
            font-size: 14px;
            color: #4285f4;
            font-weight: 500;
        }
        
        .content {
            padding: 30px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            margin-top: 40px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #fafafa;
            border-radius: 8px;
            padding: 20px;
            position: relative;
        }
        
        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .metric-label {
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #666;
        }
        
        .metric-change {
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .metric-change.positive {
            color: #0f9d58;
            background: #e8f5e8;
        }
        
        .metric-change.negative {
            color: #d93025;
            background: #fce8e6;
        }
        
        .metric-value-row {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .metric-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            flex-shrink: 0;
        }
        
        .metric-icon.impressions { background: #6c5ce7; }
        .metric-icon.plays { background: #4285f4; }
        .metric-icon.clicks { background: #5f6caf; }
        .metric-icon.emails { background: #74b9ff; }
        .metric-icon.calls { background: #00b894; }
        .metric-icon.engaged { background: #6c5ce7; }
        .metric-icon.playtime { background: #4285f4; }
        .metric-icon.likes { background: #0984e3; }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            line-height: 1;
        }
        
        .chart-container {
            margin-top: 20px;
            height: 300px;
            position: relative;
        }
        
        .chart-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .chart-subtitle {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 600px) {
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="header">
            <div class="logo">vidvisor</div>
            <div class="report-period">
                <div class="label">WEEKLY REPORT</div>
                <div class="dates"><%= reportPeriod || 'June 9, 2025 - June 15, 2025' %></div>
            </div>
        </div>
        
        <div class="content">
            <div class="title">Your weekly performance metrics from <%= weekRange || 'June 9 - June 15' %> are here.</div>
            
            <div class="section-title">Performance</div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">IMPRESSIONS</span>
                        <span class="metric-change positive">↑ <%= metrics?.impressions?.change || '1,034' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon impressions">👁</div>
                        <div class="metric-value"><%= metrics?.impressions?.value || '104,402' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">PLAYS</span>
                        <span class="metric-change positive">↑ <%= metrics?.plays?.change || '230' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon plays">▶</div>
                        <div class="metric-value"><%= metrics?.plays?.value || '3,312' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">CLICKS</span>
                        <span class="metric-change negative">↓ <%= metrics?.clicks?.change || '230' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon clicks">🖱</div>
                        <div class="metric-value"><%= metrics?.clicks?.value || '426' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">EMAILS</span>
                        <span class="metric-change positive">↑ <%= metrics?.emails?.change || '30' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon emails">✉</div>
                        <div class="metric-value"><%= metrics?.emails?.value || '125' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">CALLS</span>
                        <span class="metric-change positive">↑ <%= metrics?.calls?.change || '10' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon calls">📞</div>
                        <div class="metric-value"><%= metrics?.calls?.value || '32' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">ENGAGED SESSIONS</span>
                        <span class="metric-change positive">↑ <%= metrics?.engaged?.change || '54' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon engaged">⚡</div>
                        <div class="metric-value"><%= metrics?.engaged?.value || '2,045' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">PLAYTIME</span>
                        <span class="metric-change positive">↑ <%= metrics?.playtime?.change || '1h 32m 10s' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon playtime">⏱</div>
                        <div class="metric-value"><%= metrics?.playtime?.value || '44h 35m 16s' %></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <span class="metric-label">LIKES</span>
                        <span class="metric-change positive">↑ <%= metrics?.likes?.change || '23' %></span>
                    </div>
                    <div class="metric-value-row">
                        <div class="metric-icon likes">👍</div>
                        <div class="metric-value"><%= metrics?.likes?.value || '45' %></div>
                    </div>
                </div>
            </div>
            
            <div class="section-title">Daily Playtime</div>
            
            <div class="chart-container">
                <div class="chart-subtitle">PLAYTIME (HOURS)</div>
                <canvas id="playtimeChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Chart.js configuration for the daily playtime chart
        const ctx = document.getElementById('playtimeChart').getContext('2d');
        
        // Sample data - replace with actual EJS data
        const chartData = {
            labels: [
                '<%= chartData?.labels?.[0] || "JUNE 9" %>',
                '<%= chartData?.labels?.[1] || "JUNE 10" %>',
                '<%= chartData?.labels?.[2] || "JUNE 11" %>',
                '<%= chartData?.labels?.[3] || "JUNE 12" %>',
                '<%= chartData?.labels?.[4] || "JUNE 13" %>',
                '<%= chartData?.labels?.[5] || "JUNE 14" %>',
                '<%= chartData?.labels?.[6] || "JUNE 15" %>'
            ],
            datasets: [{
                data: [
                    <%= chartData?.values?.[0] || 5.5 %>,
                    <%= chartData?.values?.[1] || 3.2 %>,
                    <%= chartData?.values?.[2] || 6.1 %>,
                    <%= chartData?.values?.[3] || 7.3 %>,
                    <%= chartData?.values?.[4] || 7.8 %>,
                    <%= chartData?.values?.[5] || 2.8 %>,
                    <%= chartData?.values?.[6] || 8.1 %>
                ],
                borderColor: '#333',
                backgroundColor: 'transparent',
                borderWidth: 2,
                pointBackgroundColor: '#4285f4',
                pointBorderColor: '#4285f4',
                pointRadius: 4,
                pointHoverRadius: 6,
                tension: 0.4,
                fill: false
            }]
        };
        
        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: 10,
                        ticks: {
                            stepSize: 2,
                            color: '#666',
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return value + ':00';
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#4285f4',
                        hoverBorderColor: '#4285f4'
                    }
                }
            }
        };
        
        new Chart(ctx, config);
    </script>
</body>
</html>

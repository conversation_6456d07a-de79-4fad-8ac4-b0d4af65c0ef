<div class="section-title">Daily Plays</div>

<div class="chart-container">
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4285f4;"></div>
            <span>CURRENT WEEK</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #fbbc04;"></div>
            <span>PREVIOUS WEEK</span>
        </div>
    </div>
    <div class="chart-subtitle">COUNT</div>
    <canvas id="<%= chartId %>" class="chart-canvas"></canvas>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('<%= chartId %>').getContext('2d');
        
        const chartData = {
            labels: ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'],
            datasets: [
                {
                    label: 'CURRENT WEEK',
                    data: <%= JSON.stringify(chartData?.currentWeek || [5800, 7200, 6200, 5600, 4800, 5400, 6600]) %>,
                    backgroundColor: '#4285f4',
                    borderColor: '#4285f4',
                    borderWidth: 1
                },
                {
                    label: 'PREVIOUS WEEK',
                    data: <%= JSON.stringify(chartData?.previousWeek || [4000, 6400, 4800, 6200, 3600, 4000, 7200]) %>,
                    backgroundColor: '#fbbc04',
                    borderColor: '#fbbc04',
                    borderWidth: 1
                }
            ]
        };
        
        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: 8000,
                        ticks: {
                            stepSize: 2000,
                            color: '#666',
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return (value / 1000) + 'k';
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                },
                barPercentage: 0.8,
                categoryPercentage: 0.9
            }
        };
        
        new Chart(ctx, config);
    });
</script>

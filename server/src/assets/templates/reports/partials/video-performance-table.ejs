<div class="section-title">Your Videos</div>

<table class="video-table">
    <thead>
        <tr>
            <th>POSITION</th>
            <th>TITLE</th>
            <th>LENGTH</th>
            <th>PLAYS</th>
            <th>CLICKS</th>
            <th>EMAILS</th>
            <th>CALLS</th>
            <th>PLAYTIME</th>
            <th>SCORE</th>
        </tr>
    </thead>
    <tbody>
        <% (videos || [
            { position: 1, change: 'up', changeValue: 1, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 2, change: 'up', changeValue: 1, title: 'Really Long Video Title that will go...', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 3, change: 'same', changeValue: 0, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 4, change: 'same', changeValue: 0, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 5, change: 'up', changeValue: 2, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 6, change: 'same', changeValue: 0, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 7, change: 'same', changeValue: 0, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 8, change: 'same', changeValue: 0, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 9, change: 'up', changeValue: 1, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 },
            { position: 10, change: 'same', changeValue: 0, title: 'Video Title', length: '1:45', plays: 202, clicks: 30, emails: 12, calls: 12, playtime: '2h 30m 13s', score: 94 }
        ]).forEach(function(video) { %>
        <tr>
            <td>
                <span class="video-position"><%= video.position %></span>
                <% if (video.change === 'up') { %>
                    <span class="video-position-change up">↑ <%= video.changeValue %></span>
                <% } else if (video.change === 'down') { %>
                    <span class="video-position-change down">↓ <%= video.changeValue %></span>
                <% } else { %>
                    <span class="video-position-change same">-</span>
                <% } %>
            </td>
            <td>
                <div class="video-info">
                    <div class="video-thumbnail"></div>
                    <div class="video-title"><%= video.title %></div>
                </div>
            </td>
            <td><%= video.length %></td>
            <td><%= video.plays %></td>
            <td><%= video.clicks %></td>
            <td><%= video.emails %></td>
            <td><%= video.calls %></td>
            <td><%= video.playtime %></td>
            <td><%= video.score %></td>
        </tr>
        <% }); %>
    </tbody>
</table>

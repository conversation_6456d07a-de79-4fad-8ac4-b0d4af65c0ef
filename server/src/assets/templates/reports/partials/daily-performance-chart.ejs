<div class="section-title">Daily Performance</div>

<div class="chart-container">
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4285f4;"></div>
            <span>EMAILS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ea4335;"></div>
            <span>CALLS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #34a853;"></div>
            <span>CLICKS</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #fbbc04;"></div>
            <span>LIKES</span>
        </div>
    </div>
    <div class="chart-subtitle">COUNT</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="<%= chartId %>" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Use requestIdleCallback for better performance
        const initChart = function() {
            const ctx = document.getElementById('<%= chartId %>').getContext('2d');

        const chartData = {
            labels: [
                '<%= chartData?.labels?.[0] || "JUNE 9" %>',
                '<%= chartData?.labels?.[1] || "JUNE 10" %>',
                '<%= chartData?.labels?.[2] || "JUNE 11" %>',
                '<%= chartData?.labels?.[3] || "JUNE 12" %>',
                '<%= chartData?.labels?.[4] || "JUNE 13" %>',
                '<%= chartData?.labels?.[5] || "JUNE 14" %>',
                '<%= chartData?.labels?.[6] || "JUNE 15" %>'
            ],
            datasets: [
                {
                    label: 'EMAILS',
                    data: <%= JSON.stringify(chartData?.emails || [11, 9, 13, 15, 16, 8, 17]) %>,
                    borderColor: '#4285f4',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#4285f4',
                    pointBorderColor: '#4285f4',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'CALLS',
                    data: <%= JSON.stringify(chartData?.calls || [4, 6, 8, 6, 5, 9, 9]) %>,
                    borderColor: '#ea4335',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#ea4335',
                    pointBorderColor: '#ea4335',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'CLICKS',
                    data: <%= JSON.stringify(chartData?.clicks || [15, 14, 12, 16, 12, 11, 14]) %>,
                    borderColor: '#34a853',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#34a853',
                    pointBorderColor: '#34a853',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'LIKES',
                    data: <%= JSON.stringify(chartData?.likes || [7, 5, 5, 9, 8, 5, 11]) %>,
                    borderColor: '#fbbc04',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#fbbc04',
                    pointBorderColor: '#fbbc04',
                    pointRadius: 4,
                    tension: 0.4,
                    fill: false
                }
            ]
        };

        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false, // Disable animations for performance
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: 20,
                        ticks: {
                            stepSize: 5,
                            color: '#666',
                            font: {
                                size: 11
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                }
            }
        };

            new Chart(ctx, config);
        };

        // Use requestIdleCallback if available, otherwise fallback to setTimeout
        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>

<div class="section-title">Daily Playtime</div>

<div class="chart-container">
    <div class="chart-subtitle">PLAYTIME (HOURS)</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="<%= chartId %>" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Use requestIdleCallback for better performance
        const initChart = function() {
            const ctx = document.getElementById('<%= chartId %>').getContext('2d');

        const chartData = {
            labels: [
                '<%= chartData?.labels?.[0] || "JUNE 9" %>',
                '<%= chartData?.labels?.[1] || "JUNE 10" %>',
                '<%= chartData?.labels?.[2] || "JUNE 11" %>',
                '<%= chartData?.labels?.[3] || "JUNE 12" %>',
                '<%= chartData?.labels?.[4] || "JUNE 13" %>',
                '<%= chartData?.labels?.[5] || "JUNE 14" %>',
                '<%= chartData?.labels?.[6] || "JUNE 15" %>'
            ],
            datasets: [{
                data: [
                    <%= chartData?.values?.[0] || 5.5 %>,
                    <%= chartData?.values?.[1] || 3.2 %>,
                    <%= chartData?.values?.[2] || 6.1 %>,
                    <%= chartData?.values?.[3] || 7.3 %>,
                    <%= chartData?.values?.[4] || 7.8 %>,
                    <%= chartData?.values?.[5] || 2.8 %>,
                    <%= chartData?.values?.[6] || 8.1 %>
                ],
                borderColor: '#333',
                backgroundColor: 'transparent',
                borderWidth: 2,
                pointBackgroundColor: '#4285f4',
                pointBorderColor: '#4285f4',
                pointRadius: 4,
                pointHoverRadius: 6,
                tension: 0.4,
                fill: false
            }]
        };

        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false, // Disable animations for performance
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#666',
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        min: 0,
                        max: 10,
                        ticks: {
                            stepSize: 2,
                            color: '#666',
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return value + ':00';
                            }
                        },
                        grid: {
                            color: '#f0f0f0',
                            borderDash: [2, 2]
                        },
                        border: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#4285f4',
                        hoverBorderColor: '#4285f4'
                    }
                }
            }
        };

            new Chart(ctx, config);
        };

        // Use requestIdleCallback if available, otherwise fallback to setTimeout
        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>

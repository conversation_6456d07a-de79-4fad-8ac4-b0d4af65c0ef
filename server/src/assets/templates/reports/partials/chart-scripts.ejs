<script>
    Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif';
    Chart.defaults.color = '#666';

    Chart.defaults.animation = {
        duration: 0 // Disable animations
    };
    Chart.defaults.animations = {
        colors: false,
        x: false,
        y: false
    };
    Chart.defaults.transitions = {
        active: {
            animation: {
                duration: 0
            }
        }
    };

    function formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    const commonOptions = {
        responsive: true,
        maintainAspectRatio: false,
        animation: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                enabled: true,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: false,
                animation: false
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                border: {
                    display: false
                },
                ticks: {
                    color: '#666',
                    font: {
                        size: 11,
                        weight: '500'
                    }
                }
            },
            y: {
                grid: {
                    color: '#f0f0f0',
                    borderDash: [2, 2]
                },
                border: {
                    display: false
                },
                ticks: {
                    color: '#666',
                    font: {
                        size: 11
                    }
                }
            }
        },
        elements: {
            point: {
                radius: 3,
                hoverRadius: 5
            },
            line: {
                tension: 0.2
            }
        }
    };

    document.addEventListener('DOMContentLoaded', function() {
        requestIdleCallback(function() {
            console.log('Charts ready for initialization');
        }, { timeout: 1000 });
    });
</script>

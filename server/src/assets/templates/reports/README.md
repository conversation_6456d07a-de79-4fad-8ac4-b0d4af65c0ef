# VidVisor Reports Templates

This directory contains modular EJS templates for generating weekly and monthly reports with Chart.js visualizations.

## Structure

```
reports/
├── weekly-report.ejs          # Main weekly report template
├── monthly-report.ejs         # Main monthly report template
├── partials/
│   ├── styles.ejs            # All CSS styles
│   ├── header.ejs            # Report header with logo and period
│   ├── performance-metrics.ejs # Main performance metrics grid
│   ├── daily-playtime-chart.ejs # Daily playtime line chart
│   ├── daily-performance-chart.ejs # Multi-line performance chart
│   ├── daily-plays-chart.ejs # Bar chart comparing weeks
│   ├── metrics-grid.ejs      # Additional metrics grid
│   ├── video-performance-table.ejs # Video performance table
│   ├── ai-insights.ejs       # AI insights section
│   └── chart-scripts.ejs     # Common chart JavaScript
└── README.md                 # This file
```

## Usage

### Weekly Report

```javascript
const reportData = {
  reportPeriod: "June 9, 2025 - June 15, 2025",
  weekRange: "June 9 - June 15",

  // Main performance metrics
  metrics: {
    impressions: { value: "104,402", change: 1034 },
    plays: { value: "3,312", change: 230 },
    clicks: { value: "426", change: -230 },
    emails: { value: "125", change: 30 },
    calls: { value: "32", change: 10 },
    engaged: { value: "2,045", change: 54 },
    playtime: { value: "44h 35m 16s", change: "1h 32m 10s" },
    likes: { value: "45", change: 23 }
  },

  // Daily playtime chart data
  dailyPlaytimeData: {
    labels: ["JUNE 9", "JUNE 10", "JUNE 11", "JUNE 12", "JUNE 13", "JUNE 14", "JUNE 15"],
    values: [5.5, 3.2, 6.1, 7.3, 7.8, 2.8, 8.1]
  },

  // Daily performance chart data
  dailyPerformanceData: {
    labels: ["JUNE 9", "JUNE 10", "JUNE 11", "JUNE 12", "JUNE 13", "JUNE 14", "JUNE 15"],
    emails: [11, 9, 13, 15, 16, 8, 17],
    calls: [4, 6, 8, 6, 5, 9, 9],
    clicks: [15, 14, 12, 16, 12, 11, 14],
    likes: [7, 5, 5, 9, 8, 5, 11]
  },

  // Daily plays comparison chart
  dailyPlaysData: {
    currentWeek: [5800, 7200, 6200, 5600, 4800, 5400, 6600],
    previousWeek: [4000, 6400, 4800, 6200, 3600, 4000, 7200]
  },

  // Additional metrics
  metricsData: {
    engagementRate: { value: "4.23%", change: 1.20 },
    playRate: { value: "1.21%", change: 0.31 },
    clickthroughRate: { value: "4.53%", change: -0.42 },
    leadCount: { value: "4,502", change: 125 },
    playsPerSession: { value: "10", change: 2 },
    avgPlaytime: { value: "4m 2s", change: "4m 32s" }
  },

  // Video performance data
  videos: [
    {
      position: 1,
      change: 'up',
      changeValue: 1,
      title: 'Video Title',
      length: '1:45',
      plays: 202,
      clicks: 30,
      emails: 12,
      calls: 12,
      playtime: '2h 30m 13s',
      score: 94
    }
    // ... more videos
  ],

  // AI insights
  insights: [
    "Your content performs best on Tuesdays and Wednesdays, with peak engagement between 2-4 PM.",
    "Videos with CTAs in the first 30 seconds show 40% higher conversion rates."
  ]
};

// Render the report
res.render('reports/weekly-report', reportData);
```

### Monthly Report

```javascript
// Monthly data structure matches your specification:
// {
//   accountId: ObjectId
//   createdAt: DateTime
//   captureYear: Number
//   captureMonth: Number
//   totalEngagedSessions: Number
//   videos: [
//     {
//       videoId: ObjectId
//       interactiveId: ObjectId
//       plays: Integer
//       clicks: Integer
//       emailLeads: Integer
//       callLeads: Integer
//       playTimeSeconds: Integer
//       engagementScore: Integer (Percentage)
//       likes: Integer
//     }
//   ]
// }

const monthlyData = {
  reportPeriod: "June 2025",
  monthRange: "June 2025",
  // ... transformed data for template
};

res.render('reports/monthly-report', monthlyData);
```

## Features

### 📊 **Charts Included:**
- **Daily Playtime**: Line chart showing hours per day
- **Daily Performance**: Multi-line chart for emails, calls, clicks, likes
- **Daily Plays**: Bar chart comparing current vs previous period
- **All charts are responsive and interactive**

### 📈 **Metrics Sections:**
- **Performance Grid**: 8 main KPI cards with trend indicators
- **Additional Metrics**: 6 calculated metrics with descriptions
- **Video Performance Table**: Sortable table with position changes

### 🎨 **Design Features:**
- **Fully responsive** design
- **Professional styling** matching VidVisor brand
- **Emoji icons** as placeholders (easily replaceable)
- **Hover effects** and interactive elements
- **Print-friendly** styles

### 🔧 **Customization:**
- **Modular components** - easy to add/remove sections
- **Reusable partials** - shared between weekly/monthly
- **Configurable data** - all content driven by EJS variables
- **Easy theming** - centralized CSS in styles.ejs

## Adding New Sections

To add a new section:

1. Create a new partial in `partials/new-section.ejs`
2. Include it in the main template: `<%- include('partials/new-section', { data: sectionData }) %>`
3. Add any required styles to `partials/styles.ejs`
4. Add chart scripts to the partial or `partials/chart-scripts.ejs`

## Email Integration

These templates work with the existing email system:

```javascript
import { EmailModel } from '../modules/email/email.model';

const emailModel = new EmailModel();
await emailModel.sendTransactionalEmail({
  template: 'reports/weekly-report',
  to: user.email,
  subject: 'Your Weekly Performance Report',
  data: reportData,
  locale: LocaleAPI.EN_US
});
```

## API Endpoints

### Monthly Report Delivery (Testing)

```
GET /api/jobs/reports/deliver/monthly?year=2025&month=6
```

**Parameters:**
- `year`: Integer (1970-2100) - The year for the report
- `month`: Integer (1-12) - The month for the report

**Response:**
- Returns the rendered HTML report for testing
- In production, this would send the report via email

**Example:**
```bash
curl "http://localhost:3000/api/jobs/reports/deliver/monthly?year=2025&month=6"
```

**Sample Data Structure:**
The endpoint generates sample data matching your specification:
```javascript
{
  accountId: ObjectId,
  createdAt: DateTime,
  captureYear: 2025,
  captureMonth: 6,
  totalEngagedSessions: 2045,
  videos: [
    {
      videoId: ObjectId,
      interactiveId: ObjectId,
      plays: 202,
      clicks: 30,
      emailLeads: 12,
      callLeads: 12,
      playTimeSeconds: 9013,
      engagementScore: 94,
      likes: 45
    }
    // ... more videos
  ]
}

<div class="section-title">Performance</div>

<div class="metrics-grid">
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">IMPRESSIONS</span>
            <span class="metric-change <%= (metrics?.impressions?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.impressions?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.impressions?.change || 1034) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon impressions">👁</div>
            <div class="metric-value"><%= metrics?.impressions?.value || '104,402' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">PLAYS</span>
            <span class="metric-change <%= (metrics?.plays?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.plays?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.plays?.change || 230) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon plays">▶</div>
            <div class="metric-value"><%= metrics?.plays?.value || '3,312' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">CLICKS</span>
            <span class="metric-change <%= (metrics?.clicks?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.clicks?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.clicks?.change || 230) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon clicks">🖱</div>
            <div class="metric-value"><%= metrics?.clicks?.value || '426' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">EMAILS</span>
            <span class="metric-change <%= (metrics?.emails?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.emails?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.emails?.change || 30) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon emails">✉</div>
            <div class="metric-value"><%= metrics?.emails?.value || '125' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">CALLS</span>
            <span class="metric-change <%= (metrics?.calls?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.calls?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.calls?.change || 10) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon calls">📞</div>
            <div class="metric-value"><%= metrics?.calls?.value || '32' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">ENGAGED SESSIONS</span>
            <span class="metric-change <%= (metrics?.engaged?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.engaged?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.engaged?.change || 54) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon engaged">⚡</div>
            <div class="metric-value"><%= metrics?.engaged?.value || '2,045' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">PLAYTIME</span>
            <span class="metric-change <%= (metrics?.playtime?.change || '').includes('-') ? 'negative' : 'positive' %>">
                <%= (metrics?.playtime?.change || '').includes('-') ? '↓' : '↑' %> <%= metrics?.playtime?.change || '1h 32m 10s' %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon playtime">⏱</div>
            <div class="metric-value"><%= metrics?.playtime?.value || '44h 35m 16s' %></div>
        </div>
    </div>
    
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">LIKES</span>
            <span class="metric-change <%= (metrics?.likes?.change || 0) >= 0 ? 'positive' : 'negative' %>">
                <%= (metrics?.likes?.change || 0) >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics?.likes?.change || 23) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon likes">👍</div>
            <div class="metric-value"><%= metrics?.likes?.value || '45' %></div>
        </div>
    </div>
</div>

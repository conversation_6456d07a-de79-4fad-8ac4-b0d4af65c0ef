<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        line-height: 1.4;
    }
    
    .report-container {
        max-width: 900px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .header {
        background: white;
        padding: 30px;
        border-bottom: 2px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .logo {
        font-size: 32px;
        font-weight: bold;
        color: #333;
    }
    
    .report-period {
        text-align: right;
        color: #666;
    }
    
    .report-period .label {
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 4px;
    }
    
    .report-period .dates {
        font-size: 14px;
        color: #4285f4;
        font-weight: 500;
    }
    
    .content {
        padding: 30px;
    }
    
    .title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 30px;
    }
    
    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        margin-top: 40px;
    }
    
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .metric-card {
        background: #fafafa;
        border-radius: 8px;
        padding: 20px;
        position: relative;
    }
    
    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .metric-label {
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #666;
    }
    
    .metric-change {
        font-size: 11px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;
    }
    
    .metric-change.positive {
        color: #0f9d58;
        background: #e8f5e8;
    }
    
    .metric-change.negative {
        color: #d93025;
        background: #fce8e6;
    }
    
    .metric-value-row {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .metric-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
        flex-shrink: 0;
    }
    
    .metric-icon.impressions { background: #6c5ce7; }
    .metric-icon.plays { background: #4285f4; }
    .metric-icon.clicks { background: #5f6caf; }
    .metric-icon.emails { background: #74b9ff; }
    .metric-icon.calls { background: #00b894; }
    .metric-icon.engaged { background: #6c5ce7; }
    .metric-icon.playtime { background: #4285f4; }
    .metric-icon.likes { background: #0984e3; }
    
    .metric-value {
        font-size: 28px;
        font-weight: 700;
        color: #333;
        line-height: 1;
    }
    
    .chart-container {
        margin-top: 20px;
        margin-bottom: 40px;
        position: relative;
    }
    
    .chart-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }
    
    .chart-subtitle {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 15px;
    }
    
    .chart-canvas {
        height: 300px;
    }
    
    .chart-legend {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #666;
    }
    
    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    
    .additional-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }
    
    .additional-metric-card {
        background: #fafafa;
        border-radius: 8px;
        padding: 20px;
    }
    
    .additional-metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .additional-metric-label {
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #666;
    }
    
    .additional-metric-change {
        font-size: 11px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;
    }
    
    .additional-metric-change.positive {
        color: #0f9d58;
        background: #e8f5e8;
    }
    
    .additional-metric-change.negative {
        color: #d93025;
        background: #fce8e6;
    }
    
    .additional-metric-value {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        line-height: 1;
    }
    
    .additional-metric-description {
        font-size: 10px;
        color: #999;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 4px;
    }
    
    .video-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 40px;
        font-size: 14px;
    }
    
    .video-table th {
        background: #f8f9fa;
        padding: 12px 8px;
        text-align: left;
        font-weight: 600;
        font-size: 11px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #666;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .video-table td {
        padding: 12px 8px;
        border-bottom: 1px solid #f0f0f0;
        vertical-align: middle;
    }
    
    .video-table tr:hover {
        background: #fafafa;
    }
    
    .video-position {
        font-weight: 700;
        font-size: 16px;
        color: #333;
        width: 40px;
    }
    
    .video-position-change {
        font-size: 10px;
        font-weight: 600;
        margin-left: 4px;
    }
    
    .video-position-change.up {
        color: #0f9d58;
    }
    
    .video-position-change.down {
        color: #d93025;
    }
    
    .video-position-change.same {
        color: #666;
    }
    
    .video-thumbnail {
        width: 40px;
        height: 30px;
        border-radius: 4px;
        background: #ddd;
        margin-right: 12px;
        flex-shrink: 0;
    }
    
    .video-info {
        display: flex;
        align-items: center;
    }
    
    .video-title {
        font-weight: 500;
        color: #333;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .ai-insights {
        background: #1a1a1a;
        color: white;
        border-radius: 12px;
        padding: 24px;
        margin-top: 40px;
    }
    
    .ai-insights-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
    }
    
    .ai-insights-icon {
        font-size: 20px;
    }
    
    .ai-insights-title {
        font-size: 16px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .ai-insights-content {
        font-size: 14px;
        line-height: 1.6;
        color: #e0e0e0;
    }
    
    .ai-insights-content p {
        margin-bottom: 12px;
    }
    
    .ai-insights-content p:last-child {
        margin-bottom: 0;
    }
    
    @media (max-width: 768px) {
        .header {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }
        
        .metrics-grid {
            grid-template-columns: 1fr;
        }
        
        .additional-metrics {
            grid-template-columns: 1fr;
        }
        
        .content {
            padding: 20px;
        }
        
        .chart-legend {
            gap: 10px;
        }
        
        .video-table {
            font-size: 12px;
        }
        
        .video-table th,
        .video-table td {
            padding: 8px 4px;
        }
        
        .video-title {
            max-width: 120px;
        }
    }
    
    @media (max-width: 600px) {
        .video-table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
        }
    }
</style>

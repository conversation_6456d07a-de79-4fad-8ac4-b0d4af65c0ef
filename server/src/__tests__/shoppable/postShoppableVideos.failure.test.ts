import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { InteractiveCollectionModel } from "../../modules/interactiveCollection/interactiveCollection.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { IAccount } from "../../modules/account/account.interfaces";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { AccountModel } from "../../modules/account/account.model";

import * as AccountService from "../../services/mongodb/account.service";


// eslint-disable-next-line max-lines-per-function
describe("POST /shoppable-videos", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "post.shoppable.video.s.failure.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_INVALID_INPUT]. Should return 400 for missing required payload", async () => {
		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "4")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoDisplayMode", "portrait")
			.field("videoId", "videoId")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("showTitle", "true")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_INVALID_AUTHORIZATION]. Should return 401", async () => {
		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", "Bearer 123")
			.set("x-api-version", "4")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoDisplayMode", "portrait")
			.field("videoURL", "http://example.tld/example.mp4");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("[E_INTERNAL_ERROR]. Should return 500 for missing account document", async () => {
		jest.spyOn(AccountService, "readAccount2").mockResolvedValueOnce(null);

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});

	it("[E_COLLECTION_NOT_FOUND]. Should return 404 for missing collection document", async () => {

		jest.spyOn(InteractiveCollectionModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Interactive Collection not found")
		);

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_COLLECTION_NOT_FOUND);
	});

	it("should fail to create the shoppable video due to a model service failure", async () => {
		jest.spyOn(InteractiveVideoModel.prototype,	"createOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "failed to create shoppable video")
		);

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("[E_SERVICE_FAILED]. Should fail to update the shoppable collection", async () => {
		jest.spyOn(InteractiveCollectionModel.prototype, "updateOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED,	"failed to update interactive collection")
		);

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("should fail with a 403 forbidden if the number of interactive videos exceeds the limit", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const accountModel = new AccountModel(null);

		const update = {
			"subscription.maxInteractiveVideoLimit": 1
		};
		await accountModel.updateOneById(account._id.toString(), update);

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("phone", "+***********")
			.field("email", "<EMAIL>")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("productURL0", "http://example.tld/example")
			.field("productTitle0", "product title")
			.field("productDescription0", "very nice product")
			.field("subTitle0", "$100")
			.field("videoDisplayMode", "portrait")
			.field("productImageURL0", "http://example.tld/product-img.jpg");

		expect(res.statusCode).toBe(403);
		expect(res.body).toHaveProperty("error");
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);

	});

	it("[E_INVALID_INPUT]. Should return 400 for invalid textColor in captionData", async () => {
		const invalidCaptionData = JSON.stringify({
			enabled: true,
			captionText: [],
			textColor: "invalid-color",
			// Invalid hex color
			backgroundColor: "#000000",
			fontSizePx: 22,
			xPos: 0,
			yPos: 10000
		});

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("captionData", invalidCaptionData);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_INVALID_INPUT]. Should return 400 for invalid backgroundColor in captionData", async () => {
		const invalidCaptionData = JSON.stringify({
			enabled: true,
			captionText: [],
			textColor: "#FFFFFF",
			backgroundColor: "rgb(0,0,0)",
			// Invalid hex color
			fontSizePx: 22,
			xPos: 0,
			yPos: 10000
		});

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("captionData", invalidCaptionData);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("[E_INVALID_INPUT]. Should return 400 for invalid JSON in captionData", async () => {
		const invalidCaptionData = "{ invalid json }";

		const res = await supertest(expressApp)
			.post("/api/shoppable-videos")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.field("title", "abcd")
			.field("videoURL", "http://example.tld/example.mp4")
			.field("videoPosterImageURL", "http://example.tld/example.jpg")
			.field("gifURL", "http://example.tld/example.gif")
			.field("videoPosterPlayEmbedURL", "http://example.tld/example-play.jpg")
			.field("ctaText", "Shop Now")
			.field("showTitle", "true")
			.field("videoDisplayMode", "portrait")
			.field("captionData", invalidCaptionData);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});
});

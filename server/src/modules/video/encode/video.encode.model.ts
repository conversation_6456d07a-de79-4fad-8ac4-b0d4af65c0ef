import { APIErrorName } from "../../../interfaces/apiTypes";
import ffmpeg, { ffprobe } from "fluent-ffmpeg";
import path from "path";
import { APIError } from "../../../utils/helpers/apiError";
import { IFileInfo } from "../../../utils/helpers/file.helper";
import {
	CDN_DIR,
	validateBackslash
} from "../../../utils/helpers/gp.helper";
import fs from "fs/promises";
import os from "os";
import { BucketModel } from "../../bucket/bucket.model";
import { getSecrets } from "../../secrets/secrets.model";
import { VideoModel } from "../video.model";
import { VideoCreateData } from "../video.interfaces";
import {
	VideoJobModel,
	VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS
} from "../job/video.job.model";
import { JobsStatus } from "../../job/jobs.enums";
import { promises as fsPromises } from "fs";
import { IJob } from "../../job/job.interfaces";
import { AccountModel } from "../../account/account.model";
import { VideoProfile } from "../../videoProfile/videoProfile.interface";
import { VideoProfileModel } from "../../videoProfile/videoProfile.model";
import {
	BucketUploadResult,
	TempFiles,
	VideoMetadata
} from "./video.encode.interfaces";
import { CaptionData } from "src/modules/caption/caption.interface";
import { SpeechToTextModel } from "../../speechToText/speechToText.model";
import { SRTModel } from "../../srt/srt.model";

export class VideoEncodeModel {
	private lastProgressEventTime: number | null = null;
	private progressEventDeltas: number[] = [];

	constructor(private job: IJob) { }

	public async startEncoder(): Promise<void> {
		let hasAudio = false;
		let tempFiles: TempFiles | null = null;

		try {
			const job = await VideoJobModel.updateVideoEncodeStatus(this.job._id.toString(),
				{ status: JobsStatus.RUNNING, statusMessage: "Preparing video for encoding." });

			if (!job.tempFilename) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Video Encode Job tempFilename is missing.");
			}
			if (!job.accountId) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Video Encode Job accountId is missing.");
			}

			const videoProfile = await this.getVideoProfile(job.accountId.toString());
			const secrets = await getSecrets();
			const bucketModel = new BucketModel(secrets.storage.tempBucketName);
			const originalVideo = await bucketModel.fetchFileFromBucket(job.tempFilename);
			const tempDirectory = os.tmpdir();

			tempFiles = {
				videoPath: `${tempDirectory}/` + `originalVideo_${originalVideo.fileName}.mp4`,
				flacAudioPath: `${tempDirectory}/` + `audio_${originalVideo.fileName}.flac`,
				trimmedVideoPath: `${tempDirectory}/` + `trimmedVideo_${originalVideo.fileName}.mp4`,
				gifPath: `${tempDirectory}/` + `gif_${originalVideo.fileName}.gif`,
				posterPath: `${tempDirectory}/` + `poster_${originalVideo.fileName}.jpg`,
				playEmbedPosterPath: `${tempDirectory}/` + `playEmbed_${originalVideo.fileName}.jpg`
			};

			await this.encodeVideoAndAudio(originalVideo, videoProfile, tempFiles.videoPath, tempFiles.flacAudioPath);

			const videoMetaData = await this.extractMetadata(tempFiles.videoPath);

			hasAudio = videoMetaData.audioCodec !== "";

			const { startTime, gifDuration } = this.calculateGifPosition(videoMetaData.durationSeconds);
			const trimmedVideoDuration = await this.trimVideo(
				startTime,
				gifDuration,
				tempFiles.videoPath,
				tempFiles.trimmedVideoPath
			);

			await this.generateGIF(
				videoMetaData,
				trimmedVideoDuration,
				tempFiles.trimmedVideoPath,
				tempFiles.gifPath
			);

			await this.generatePoster(tempFiles.trimmedVideoPath, tempFiles.posterPath);

			await this.generatePlayEmbedPoster(
				videoMetaData,
				tempFiles.trimmedVideoPath,
				tempFiles.playEmbedPosterPath
			);

			const uploadedVideoResult = await this.uploadVideoToBucket(
				originalVideo.fileName,
				job.accountId.toString(),
				tempFiles.videoPath
			);

			const uploadedPosterResult = await this.uploadPosterToBucket(
				originalVideo.fileName,
				job.accountId.toString(),
				tempFiles.posterPath
			);

			const uploadedGifResult = await this.uploadGifToBucket(
				originalVideo.fileName,
				tempFiles.gifPath
			);

			const uploadedPlayEmbedPosterResult = await this.uploadPlayEmbedPosterToBucket(
				originalVideo.fileName,
				job.accountId.toString(),
				tempFiles.playEmbedPosterPath
			);

			let uploadedFlacResult: BucketUploadResult = { path: "", url: "" };
			let captionData: CaptionData | undefined = undefined;
			if (hasAudio) {
				uploadedFlacResult = await this.uploadFlacAudioToBucket(
					originalVideo.fileName,
					job.accountId.toString(),
					tempFiles.flacAudioPath
				);

				const speechToTextModel = new SpeechToTextModel(
					uploadedFlacResult.path,
					secrets.speechToText.bucketName,
					secrets.speechToText.apiEndpoint,
					secrets.speechToText.credentials
				);

				await speechToTextModel.transcribe();

				const srtModel = new SRTModel();
				const subRipText = srtModel.convertFromSpeechToText(speechToTextModel.data);
				const captionText = await srtModel.parseToCaptionText(subRipText);

				//Default caption settings with each new video
				captionData = {
					enabled: true,
					captionText: captionText,
					textColor: "#FFFFFF",
					backgroundColor: "#000000",
					fontSizePx: 14,
					xPos: 0,
					yPos: 10000
				};
			}

			const videoModel = new VideoModel(null);
			const VideoCreateData: VideoCreateData = {
				accountId: job.accountId,
				publicPosterURL: uploadedPosterResult.url,
				publicGifURL: uploadedGifResult.url,
				publicPosterPlayEmbedURL: uploadedPlayEmbedPosterResult.url,
				publicVideoURL: uploadedVideoResult.url,
				posterFileLocation: uploadedPosterResult.path,
				posterPlayEmbedFileLocation: uploadedPlayEmbedPosterResult.path,
				gifFileLocation: uploadedGifResult.path,
				videoFileLocation: uploadedVideoResult.path,
				videoWidthPx: videoMetaData.width,
				videoHeightPx: videoMetaData.height,
				fileSizeBytes: videoMetaData.fileSizeBytes,
				durationSeconds: videoMetaData.durationSeconds,
				videoCodec: videoMetaData.videoCodec,
				videoBitrate: videoMetaData.videoBitrate,
				frameRate: videoMetaData.frameRate,
				audioCodec: videoMetaData.audioCodec,
				audioSampleRate: videoMetaData.audioSampleRate,
				audioChannels: videoMetaData.audioChannels,
				videoProfile: videoProfile._id,
				captionData: captionData
			};
			const video = await videoModel.createOne(VideoCreateData);

			await VideoJobModel.updateVideoEncodeStatus(this.job._id.toString(),
				{
					progressPercent: 100,
					videoId: video._id,
					status: JobsStatus.COMPLETE,
					statusMessage: "Video Encode Job has completed successfully."
				});
			const callbackUrl = job.callbackInfo?.callbackUrl;

			if (job.callbackInfo != undefined && callbackUrl != undefined) {
				try {
					const payload = {
						videoData: video,
						callbackData: job.callbackInfo.callbackData ?? ""
					};

					await fetch(callbackUrl, {
						method: "POST",
						body: JSON.stringify(payload)
					});
				} catch (error) {
					APIError.fromUnknownError(`Failed to send video webhook to: ${callbackUrl}\n${error}`).log();
				}
			}

		} catch (error: unknown) {
			await VideoJobModel.updateVideoEncodeStatus(this.job._id.toString(),
				{ statusMessage: "Video Encode Job has failed.", status: JobsStatus.FAILED });
			throw error;
		} finally {
			await this.removeTempFiles(tempFiles, hasAudio);
		}
	}

	protected async getVideoProfile(accountId: string): Promise<VideoProfile> {
		const accountModel = new AccountModel(null);
		const account = await accountModel.readOneById(accountId);

		const videoProfileModel = new VideoProfileModel(null);
		let videoProfile: VideoProfile;
		try {
			if (account.videoProfile) {
				videoProfile = await videoProfileModel.readOneById(account.videoProfile.toString());
			} else {
				throw new Error("Missing videoProfile in the account document.");
			}
		} catch {
			videoProfile = await videoProfileModel.readDefault();
			await accountModel.updateOneById(accountId, { videoProfile: videoProfile._id });
		}

		if (!videoProfile) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
				"No video profile found for account, and no default profile available.");
		}

		return videoProfile;
	}

	protected async waitForFileWrite(filePath: string): Promise<void> {
		const MAX_WAIT_MS = 10_000;
		const POLL_INTERVAL_MS = 500;
		const CONSECUTIVE_CHECKS_REQUIRED = 2;
		const startTime = Date.now();
		let previousSize = -1;
		let stableChecks = 0;

		return new Promise((resolve, reject) => {
			const checkFile = async (): Promise<void> => {
				try {
					const stats = await fsPromises.stat(filePath);

					if (stats.size === previousSize) {
						stableChecks++;
					} else {
						stableChecks = 0;
					}
					previousSize = stats.size;

					if (stableChecks >= CONSECUTIVE_CHECKS_REQUIRED) {
						return resolve();
					}

					if (Date.now() - startTime > MAX_WAIT_MS) {
						return reject(new APIError(APIErrorName.E_INTERNAL_ERROR,
							`Timeout waiting for file "${filePath}" to finish writing.`));
					}
					setTimeout(checkFile, POLL_INTERVAL_MS);

				} catch (error: unknown) {
					if ((error as NodeJS.ErrnoException).code === "ENOENT") {
						if (Date.now() - startTime > MAX_WAIT_MS) {
							return reject(new APIError(APIErrorName.E_INTERNAL_ERROR,
								`File "${filePath}" did not appear within ${MAX_WAIT_MS}ms.`));
						}
						setTimeout(checkFile, POLL_INTERVAL_MS);
						return;
					}
					return reject(APIError.fromUnknownError(error));
				}
			};
			checkFile();
		});
	}

	protected async handleFfmpegProgress(progressPercent: number): Promise<void> {
		const now = Date.now();
		if (this.lastProgressEventTime !== null) {
			const delta = now - this.lastProgressEventTime;
			this.progressEventDeltas.push(delta);
		}
		this.lastProgressEventTime = now;

		let meanDelta = VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS;
		if (this.progressEventDeltas.length > 0) {
			const sum = this.progressEventDeltas.reduce((acc, cur) => acc + cur, 0);
			meanDelta = sum / this.progressEventDeltas.length;
		}

		if (meanDelta < VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS) {
			meanDelta = VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS;
		}

		await VideoJobModel.updateVideoEncodeStatus(this.job._id.toString(), {
			progressPercent: progressPercent,
			statusMessage: `Encoding video...${progressPercent}%`,
			nextStatusCheck: now + Math.round(meanDelta)
		});
	}

	private async encodeVideoAndAudio(
		originalVideo: IFileInfo,
		videoProfile: VideoProfile,
		tempVideoPath: string,
		tempFlacAudioPath: string
	): Promise<void> {
		const originalVideoMetaData = await this.extractMetadata(originalVideo.filePath);
		const isLandscape = originalVideoMetaData.width > originalVideoMetaData.height;
		const dominantDimension = isLandscape ? "ih" : "iw";
		const scalingFilter = `scale='if(gt(${dominantDimension},${videoProfile.scale}),${videoProfile.scale},-1)':-2`;

		const hasAudio = originalVideoMetaData.audioCodec !== "";

		const ffmpegVideoOptions = [
			"-profile:v baseline",
			`-level ${videoProfile.level}`,
			`-pix_fmt ${videoProfile.pixelFormat}`,
			`-crf ${videoProfile.constantRateFactor}`,
			`-preset ${videoProfile.preset}`,
			"-movflags +faststart",
			`-vf ${scalingFilter}`,
			"-map 0:v:0"
		];

		if (hasAudio) {
			ffmpegVideoOptions.push("-map 0:a:0");
			ffmpegVideoOptions.push(`-b:a ${videoProfile.audioBitrate}`);
		}

		return new Promise((resolve, reject) => {
			const command = ffmpeg()
				.input(originalVideo.filePath)
				.output(tempVideoPath)
				.videoCodec("libx264")
				.fps(30)
				.outputOptions(ffmpegVideoOptions);

			if (hasAudio) {
				command.audioCodec("aac");

				command.output(tempFlacAudioPath)
					.noVideo()
					.audioCodec("flac")
					.outputOptions([
						"-map 0:a:0",
						"-ac 1",
						"-ar 16000",
						"-sample_fmt s16",
						"-af loudnorm=I=-16:TP=-1.5:LRA=11"
					]);
			}

			command
				.on("progress", async (progress) => {
					try {
						if (progress.percent > 0) {
							await this.handleFfmpegProgress(progress.percent);
						}
					} catch (error: unknown) {
						return reject(APIError.fromUnknownError(error));
					}
				})
				.on("end", async () => {
					// Wait for video file and audio file (if it exists)
					const filesToWait = [tempVideoPath];
					if (hasAudio) {
						filesToWait.push(tempFlacAudioPath);
					}
					await Promise.all(filesToWait.map(file => this.waitForFileWrite(file)));

					resolve();
				})
				.on("error", (error: unknown, stdout, stderr) => {
					const apiError = APIError.fromUnknownError(error).setDetail({
						stdout: stdout,
						stderr: stderr
					});

					reject(apiError);
				})
				.run();
		});
	}

	private async extractMetadata(filePath: string): Promise<VideoMetadata> {
		return new Promise((resolve, reject) => {
			ffprobe(filePath, (err, metadata) => {
				if (err) {
					return reject(new APIError(APIErrorName.E_INTERNAL_ERROR,
						`ffprobe error reading metadata: ${err.message}`));
				}

				const videoStream = metadata.streams.find(stream => stream.codec_type === "video");
				const audioStream = metadata.streams.find(stream => stream.codec_type === "audio");

				if (videoStream && videoStream.width && videoStream.height) {
					resolve({
						width: videoStream.width,
						height: videoStream.height,
						fileSizeBytes: metadata.format.size || 0,
						durationSeconds: metadata.format.duration || 0,
						videoCodec: videoStream.codec_name || "",
						videoBitrate: parseInt(videoStream.bit_rate || "0", 10),
						frameRate: parseFloat(videoStream.r_frame_rate || "0"),
						audioCodec: audioStream?.codec_name || "",
						audioSampleRate: audioStream?.sample_rate || 0,
						audioChannels: audioStream?.channels || 0
					});
				} else {
					reject(new APIError(APIErrorName.E_INTERNAL_ERROR, "Unable to extract video metadata"));
				}
			});
		});
	}

	private async trimVideo(
		startTime: number,
		duration: number,
		inputVideoPath: string,
		outputVideoPath: string
	): Promise<number> {
		return new Promise((resolve, reject) => {
			ffmpeg(inputVideoPath)
				.setStartTime(startTime)
				.duration(duration)
				.outputOptions(["-c", "copy"])
				.output(outputVideoPath)
				.on("error", (err) => {
					reject(new APIError(APIErrorName.E_INTERNAL_ERROR, `FFmpeg error trimming video: ${err.message}`));
				})
				.on("end", () => resolve(duration))
				.run();
		});
	}

	private calculateGifPosition(durationSeconds: number): { startTime: number, gifDuration: number; } {
		if (durationSeconds > 6) {
			return { startTime: durationSeconds / 2 - 1.5, gifDuration: 3 };
		} else if (durationSeconds > 3) {
			return { startTime: durationSeconds - 3, gifDuration: 3 };
		}
		return { startTime: 0, gifDuration: durationSeconds };
	}

	private async generatePoster(inputVideoPath: string, outputImagePath: string): Promise<void> {
		return new Promise((resolve, reject) => {
			ffmpeg(inputVideoPath)
				.outputOptions(["-vframes 1", "-an"])
				.output(outputImagePath)
				.on("error", (err) => reject(new APIError(APIErrorName.E_INTERNAL_ERROR,
					`FFmpeg error generating poster: ${err.message}`)))
				.on("end", async () => {
					await this.waitForFileWrite(outputImagePath);
					resolve();
				})
				.run();
		});
	}

	private async generateGIF(
		videoMetaData: VideoMetadata,
		gifDuration: number,
		inputVideoPath: string,
		outputGifPath: string
	): Promise<void> {
		const isLandscape = videoMetaData.width > videoMetaData.height;
		const iconPath = path.resolve(__dirname, "../../../assets/icon-play.png");
		try {
			await fs.access(iconPath);
		} catch (err) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				`Failed to generate Gif. PNG file not found at path: ${iconPath}`
			);
		}

		/*
		[0:v]: refers to the first video stream, which is the input video.

		scale=-2:350[scaled]:
		Resizes the video to have a height of 350 pixels, keeping the aspect ratio intact.

		pad=w=iw+10:h=ih+10:x=5:y=5:color=white[bordered]:
		Adds a white border around the video, padding it by 10 pixels (5 on each side).

		pad=600:400:(ow-iw)/2:(oh-ih)/2:black[final]:
		Pads the video further to fit within a 600x400 frame, centering it, and filling the background with black.

		[final][1:v]overlay=(W-w)/2:(H-h)/2:format=auto:
		Overlays an icon (second input) at the center of the video.

		split[s0][s1]: Splits the video into two streams, so the palette can be generated and then applied.

		palettegen[p]: Generates a color palette from the first stream.

		[s1][p]paletteuse: Applies the generated palette to the second stream, making the GIF more color-efficient.
		*/
		const portraitFilter = `
        [0:v]scale=-2:350[scaled];
        [scaled]pad=w=iw+10:h=ih+10:x=5:y=5:color=white[bordered];
        [bordered]pad=600:400:(ow-iw)/2:(oh-ih)/2:black[final];
        [final][1:v]overlay=(W-w)/2:(H-h)/2:format=auto,split[s0][s1];
        [s0]palettegen[p];
        [s1][p]paletteuse
    `;

		/*
		[0:v]scale=600:400:force_original_aspect_ratio=increase:
		Resizes the video to fit within a 600x400 frame, keeping the aspect ratio intact.

		crop=600:400[base]: Crops the video to ensure it fits exactly within the 600x400 frame.

		[base][1:v]overlay=(W-w)/2:(H-h)/2:format=auto:
		Overlays the icon at the center of the video, after resizing and cropping.

		split[s0][s1]: Splits the video stream for palette generation and application.

		palettegen[p]: Generates a color palette from the first stream.

		[s1][p]paletteuse: Applies the generated color palette to the second stream.
		*/
		const landscapeFilter = `
        [0:v]scale=600:400:force_original_aspect_ratio=increase,crop=600:400[base];
        [base][1:v]overlay=(W-w)/2:(H-h)/2:format=auto,split[s0][s1];
        [s0]palettegen[p];
        [s1][p]paletteuse
    `;
		const complexFilter = isLandscape ? landscapeFilter : portraitFilter;

		return new Promise((resolve, reject) => {
			ffmpeg(inputVideoPath)
				.setStartTime(0)
				.duration(gifDuration)
				.input(iconPath)
				.complexFilter([complexFilter])
				.outputOptions([
					"-r", "5"
				])
				.toFormat("gif")
				.output(outputGifPath)
				.on("error", (err) => {
					reject(new APIError(
						APIErrorName.E_INTERNAL_ERROR,
						`FFmpeg error generating GIF: ${err.message}`
					));
				})
				.on("end", async () => {
					await this.waitForFileWrite(outputGifPath);
					resolve();
				})
				.run();
		});
	}

	private async generatePlayEmbedPoster(
		videoMetaData: VideoMetadata,
		inputVideoPath: string,
		outputImagePath: string
	): Promise<void> {
		const isLandscape = videoMetaData.width > videoMetaData.height;
		const iconPath = path.resolve(__dirname, "../../../assets/icon-play.png");

		try {
			await fs.access(iconPath);
		} catch (err) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				`PNG file not found at path: ${iconPath}`
			);
		}

		/*
		This is similar to the one used in GIF generation but with slight modifications.
		It scales the video to have a height of 350 pixels while keeping the aspect ratio,
		pads it to 600x400 with a black background, and overlays an icon at the center of the video.
		*/
		const portraitFilter = `
        [0:v]scale=-2:350[scaled];
        [scaled]pad=w=iw+10:h=ih+10:x=5:y=5:color=white[bordered];
        [bordered]pad=600:400:(ow-iw)/2:(oh-ih)/2:black[final];
        [final][1:v]overlay=(W-w)/2:(H-h)/2:format=auto
        `;

		/*
		Similar to the GIF generation filter, it scales and crops the video to fit within a 600x400 frame.
		It then overlays the play icon at the center of the video frame.
		*/
		const landscapeFilter = `
        [0:v]scale=600:400:force_original_aspect_ratio=increase,crop=600:400[base];
        [base][1:v]overlay=(W-w)/2:(H-h)/2:format=auto
        `;

		const complexFilter = isLandscape ? landscapeFilter : portraitFilter;

		return new Promise((resolve, reject) => {
			ffmpeg(inputVideoPath)
				.setStartTime(0)
				.input(iconPath)
				.complexFilter([complexFilter])
				.outputOptions([
					"-frames:v 1",
					"-q:v 5"
				])
				.output(outputImagePath)
				.on("error", (err) => {
					reject(new APIError(
						APIErrorName.E_INTERNAL_ERROR,
						`FFmpeg error generating play embed poster image: ${err.message}`
					));
				})
				.on("end", async () => {
					await this.waitForFileWrite(outputImagePath);
					resolve();
				})
				.run();
		});
	}

	private async uploadVideoToBucket(
		filename: string,
		accountId: string,
		videoFilePath: string
	): Promise<BucketUploadResult> {
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.bucketName);
		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;
		const videoLocation = CDN_DIR.VIDEOS + accountId + "/" + filename + ".mp4";

		await bucketModel.uploadFileToBucketFromStream(
			videoFilePath,
			"video/mp4",
			videoLocation,
			cacheControl
		);

		const result: BucketUploadResult = {
			path: videoLocation,
			url: `${validateBackslash(secrets.cdn.host)}${videoLocation}`
		};

		return result;
	}

	private async uploadPosterToBucket(
		filename: string,
		accountId: string,
		posterFilePath: string
	): Promise<BucketUploadResult> {
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.bucketName);
		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;
		const posterLocation = CDN_DIR.POSTERS + accountId + "/" + filename + ".jpg";

		await bucketModel.uploadFileToBucketFromStream(
			posterFilePath,
			"image/jpeg",
			posterLocation,
			cacheControl
		);

		const result: BucketUploadResult = {
			path: posterLocation,
			url: `${validateBackslash(secrets.cdn.host)}${posterLocation}`
		};

		return result;
	}

	private async uploadGifToBucket(
		filename: string,
		gifFilePath: string
	): Promise<BucketUploadResult> {
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.bucketName);
		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;
		const gifLocation = CDN_DIR.GIFS + filename + ".gif";

		await bucketModel.uploadFileToBucketFromStream(
			gifFilePath,
			"image/gif",
			gifLocation,
			cacheControl
		);

		const result: BucketUploadResult = {
			path: gifLocation,
			url: `${validateBackslash(secrets.cdn.host)}${gifLocation}`
		};

		return result;
	}

	private async uploadPlayEmbedPosterToBucket(
		filename: string,
		accountId: string,
		playEmbedPosterFilePath: string
	): Promise<BucketUploadResult> {
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(secrets.storage.bucketName);
		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;
		const playEmbedPosterLocation = CDN_DIR.POSTERS + accountId + "/play-embed/" + filename + ".jpg";

		await bucketModel.uploadFileToBucketFromStream(
			playEmbedPosterFilePath,
			"image/jpeg",
			playEmbedPosterLocation,
			cacheControl
		);

		const result: BucketUploadResult = {
			path: playEmbedPosterLocation,
			url: `${validateBackslash(secrets.cdn.host)}${playEmbedPosterLocation}`
		};

		return result;
	}

	private async uploadFlacAudioToBucket(
		filename: string,
		accountId: string,
		flacAudioFilePath: string
	): Promise<BucketUploadResult> {
		const secrets = await getSecrets();
		const bucketModel = new BucketModel(
			secrets.speechToText.bucketName,
			secrets.speechToText.storageUrl,
			secrets.speechToText.credentials
		);
		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;
		const flacAudioLocation = `flac/${accountId}/${filename}.flac`;

		await bucketModel.uploadFileToBucketFromStream(
			flacAudioFilePath,
			"audio/flac",
			flacAudioLocation,
			cacheControl
		);

		const result: BucketUploadResult = {
			path: flacAudioLocation,
			url: `${validateBackslash(secrets.speechToText.storageUrl)}${flacAudioLocation}`
		};

		return result;
	}

	private async removeTempFiles(tempFiles: TempFiles | null, hasAudio: boolean): Promise<void> {
		try {
			if (tempFiles) {
				const files = [
					tempFiles.videoPath,
					tempFiles.trimmedVideoPath,
					tempFiles.posterPath,
					tempFiles.gifPath,
					tempFiles.playEmbedPosterPath
				];

				if (hasAudio) {
					files.push(tempFiles.flacAudioPath);
				}

				await Promise.all(files.map(file => () => {
					fsPromises.unlink(file)
						.catch((error: unknown) => {
							if (error instanceof Error && error.message.includes("ENOENT")) {
								return;
							}

							throw error;
						});
				}));
			}
		} catch (error: unknown) {
			APIError.fromUnknownError(error).log();
		}
	}
}

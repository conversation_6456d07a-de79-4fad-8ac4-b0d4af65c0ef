import {
	Request,
	Response
} from "express";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import Joi from "joi";
import * as path from "path";
import ejs from "ejs";
import { PDFService } from "../../../services/pdf/pdf.service";
import {
	getSecrets,
	ISecrets
} from "../../secrets/secrets.model";
import { validateBackslash } from "../../../utils/helpers/gp.helper";

interface MonthlyReportPayload {
	year: number;
	month: number;
	format?: 'html' | 'pdf';
}

const MonthlyReportPayloadSchema = Joi.object<MonthlyReportPayload>({
	year: Joi.number().integer().min(1970).max(2100).required(),
	month: Joi.number().integer().min(1).max(12).required(),
	format: Joi.string().valid('html', 'pdf').optional().default('html')
});

export class JobReportsController {

	public deliverMonthly = async (request: Request, response: Response): Promise<Response> => {
		let validPayload: MonthlyReportPayload;

		try {
			validPayload = await MonthlyReportPayloadSchema.validateAsync({
				year: parseInt(request.query.year as string),
				month: parseInt(request.query.month as string),
				format: request.query.format as string || 'html'
			});
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.setResponse(response);
		}

		try {
			// Get secrets for CDN host
			const secrets = await getSecrets();

			const monthlyData = this.generateSampleMonthlyData(validPayload.year, validPayload.month);

			// Transform the data for the report template
			const reportData = this.transformDataForTemplate(monthlyData, validPayload.year, validPayload.month);

			// Render the HTML template
			const templatePath = path.resolve(
				__dirname,
				"../../../assets/templates/reports/monthly-report.ejs"
			);

			const htmlContent = await ejs.renderFile(templatePath, {
				...reportData,
				host: this.getHost(secrets)
			});

			// Return based on requested format
			if (validPayload.format === 'pdf') {
				return await this.generatePDFResponse(htmlContent, response, validPayload.year, validPayload.month);
			} else {
				// Return HTML for testing
				return response.status(200).send(htmlContent);
			}

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).setResponse(response);
		}
	};

	private async generatePDFResponse(
		htmlContent: string,
		response: Response,
		year: number,
		month: number
	): Promise<Response> {
		const pdfService = new PDFService();

		try {
			// Generate PDF with optimized options for reports
			const pdfBuffer = await pdfService.generatePDFFromHTML(
				htmlContent,
				PDFService.getReportPDFOptions()
			);

			// Set response headers for PDF download
			const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
							  'July', 'August', 'September', 'October', 'November', 'December'];
			const filename = `VidVisor-Monthly-Report-${monthNames[month - 1]}-${year}.pdf`;

			response.setHeader('Content-Type', 'application/pdf');
			response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
			response.setHeader('Content-Length', pdfBuffer.length);

			return response.status(200).send(pdfBuffer);

		} finally {
			// Always close the PDF service
			await pdfService.close();
		}
	}

	private generateSampleMonthlyData(year: number, month: number) {
		// Generate sample data matching your structure
		return {
			accountId: "507f1f77bcf86cd799439011", // Sample ObjectId
			createdAt: new Date(),
			captureYear: year,
			captureMonth: month,
			totalEngagedSessions: Math.floor(Math.random() * 5000) + 1000,
			videos: [
				{
					videoId: "507f1f77bcf86cd799439012",
					interactiveId: "507f1f77bcf86cd799439013",
					plays: Math.floor(Math.random() * 1000) + 100,
					clicks: Math.floor(Math.random() * 100) + 10,
					emailLeads: Math.floor(Math.random() * 50) + 5,
					callLeads: Math.floor(Math.random() * 20) + 2,
					playTimeSeconds: Math.floor(Math.random() * 10000) + 1000,
					engagementScore: Math.floor(Math.random() * 40) + 60, // 60-100%
					likes: Math.floor(Math.random() * 30) + 5
				},
				{
					videoId: "507f1f77bcf86cd799439014",
					interactiveId: "507f1f77bcf86cd799439015",
					plays: Math.floor(Math.random() * 800) + 80,
					clicks: Math.floor(Math.random() * 80) + 8,
					emailLeads: Math.floor(Math.random() * 40) + 4,
					callLeads: Math.floor(Math.random() * 15) + 1,
					playTimeSeconds: Math.floor(Math.random() * 8000) + 800,
					engagementScore: Math.floor(Math.random() * 35) + 55,
					likes: Math.floor(Math.random() * 25) + 3
				},
				{
					videoId: "507f1f77bcf86cd799439016",
					interactiveId: "507f1f77bcf86cd799439017",
					plays: Math.floor(Math.random() * 600) + 60,
					clicks: Math.floor(Math.random() * 60) + 6,
					emailLeads: Math.floor(Math.random() * 30) + 3,
					callLeads: Math.floor(Math.random() * 10) + 1,
					playTimeSeconds: Math.floor(Math.random() * 6000) + 600,
					engagementScore: Math.floor(Math.random() * 30) + 50,
					likes: Math.floor(Math.random() * 20) + 2
				}
			]
		};
	}

	private transformDataForTemplate(monthlyData: any, year: number, month: number) {
		const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
						  'July', 'August', 'September', 'October', 'November', 'December'];

		// Calculate totals from video data
		const totalPlays = monthlyData.videos.reduce((sum: number, video: any) => sum + video.plays, 0);
		const totalClicks = monthlyData.videos.reduce((sum: number, video: any) => sum + video.clicks, 0);
		const totalEmails = monthlyData.videos.reduce((sum: number, video: any) => sum + video.emailLeads, 0);
		const totalCalls = monthlyData.videos.reduce((sum: number, video: any) => sum + video.callLeads, 0);
		const totalLikes = monthlyData.videos.reduce((sum: number, video: any) => sum + video.likes, 0);
		const totalPlayTimeSeconds = monthlyData.videos.reduce((sum: number, video: any) => sum + video.playTimeSeconds, 0);

		// Generate some sample impressions (plays * random multiplier)
		const totalImpressions = Math.floor(totalPlays * (Math.random() * 20 + 30)); // 30-50x multiplier

		// Format playtime
		const formatPlayTime = (seconds: number) => {
			const hours = Math.floor(seconds / 3600);
			const minutes = Math.floor((seconds % 3600) / 60);
			const secs = seconds % 60;
			return `${hours}h ${minutes}m ${secs}s`;
		};

		return {
			reportPeriod: `${monthNames[month - 1]} ${year}`,
			monthRange: `${monthNames[month - 1]} ${year}`,

			// Main performance metrics
			metrics: {
				impressions: {
					value: totalImpressions.toLocaleString(),
					change: Math.floor(Math.random() * 2000) + 500
				},
				plays: {
					value: totalPlays.toLocaleString(),
					change: Math.floor(Math.random() * 400) + 100
				},
				clicks: {
					value: totalClicks.toLocaleString(),
					change: Math.floor(Math.random() * 100) + 20
				},
				emails: {
					value: totalEmails.toLocaleString(),
					change: Math.floor(Math.random() * 50) + 10
				},
				calls: {
					value: totalCalls.toLocaleString(),
					change: Math.floor(Math.random() * 20) + 5
				},
				engaged: {
					value: monthlyData.totalEngagedSessions.toLocaleString(),
					change: Math.floor(Math.random() * 200) + 50
				},
				playtime: {
					value: formatPlayTime(totalPlayTimeSeconds),
					change: formatPlayTime(Math.floor(Math.random() * 7200) + 1800)
				},
				likes: {
					value: totalLikes.toLocaleString(),
					change: Math.floor(Math.random() * 30) + 10
				}
			},

			// Generate sample chart data for the month
			dailyPlaytimeData: {
				labels: this.generateDayLabels(year, month),
				values: this.generateRandomValues(this.getDaysInMonth(year, month), 2, 12)
			},

			dailyPerformanceData: {
				labels: this.generateDayLabels(year, month),
				emails: this.generateRandomValues(this.getDaysInMonth(year, month), 5, 25),
				calls: this.generateRandomValues(this.getDaysInMonth(year, month), 1, 15),
				clicks: this.generateRandomValues(this.getDaysInMonth(year, month), 8, 30),
				likes: this.generateRandomValues(this.getDaysInMonth(year, month), 3, 20)
			},

			dailyPlaysData: {
				currentWeek: [5800, 7200, 6200, 5600, 4800, 5400, 6600],
				previousWeek: [4000, 6400, 4800, 6200, 3600, 4000, 7200]
			},

			// Additional metrics
			metricsData: {
				engagementRate: {
					value: ((monthlyData.totalEngagedSessions / totalImpressions) * 100).toFixed(2) + "%",
					change: (Math.random() * 2 - 1).toFixed(2)
				},
				playRate: {
					value: ((totalPlays / totalImpressions) * 100).toFixed(2) + "%",
					change: (Math.random() * 1 - 0.5).toFixed(2)
				},
				clickthroughRate: {
					value: ((totalClicks / totalPlays) * 100).toFixed(2) + "%",
					change: (Math.random() * 1 - 0.5).toFixed(2)
				},
				leadCount: {
					value: (totalEmails + totalCalls).toLocaleString(),
					change: Math.floor(Math.random() * 100) + 20
				},
				playsPerSession: {
					value: Math.floor(totalPlays / monthlyData.totalEngagedSessions).toString(),
					change: Math.floor(Math.random() * 3) + 1
				},
				avgPlaytime: {
					value: this.formatMinutesSeconds(Math.floor(totalPlayTimeSeconds / totalPlays)),
					change: this.formatMinutesSeconds(Math.floor(Math.random() * 120) + 30)
				}
			},

			// Transform video data for the table
			videos: monthlyData.videos.map((video: any, index: number) => ({
				position: index + 1,
				change: Math.random() > 0.5 ? 'up' : (Math.random() > 0.5 ? 'down' : 'same'),
				changeValue: Math.floor(Math.random() * 3) + 1,
				title: `Video ${index + 1}`,
				length: this.formatMinutesSeconds(Math.floor(Math.random() * 300) + 60),
				plays: video.plays,
				clicks: video.clicks,
				emails: video.emailLeads,
				calls: video.callLeads,
				playtime: this.formatMinutesSeconds(video.playTimeSeconds),
				score: video.engagementScore
			})),

			// AI insights
			insights: [
				`Your content performed exceptionally well in ${monthNames[month - 1]} ${year}, with ${totalPlays.toLocaleString()} total plays across all videos.`,
				`The engagement rate of ${((monthlyData.totalEngagedSessions / totalImpressions) * 100).toFixed(1)}% shows strong audience interest in your content.`,
				`Video performance peaked mid-month, suggesting optimal timing for future content releases.`
			]
		};
	}

	private generateDayLabels(year: number, month: number): string[] {
		const daysInMonth = this.getDaysInMonth(year, month);
		const labels = [];
		for (let i = 1; i <= Math.min(daysInMonth, 7); i++) {
			labels.push(`${month}/${i}`);
		}
		return labels;
	}

	private generateRandomValues(count: number, min: number, max: number): number[] {
		const values = [];
		for (let i = 0; i < Math.min(count, 7); i++) {
			values.push(Math.floor(Math.random() * (max - min + 1)) + min);
		}
		return values;
	}

	private getDaysInMonth(year: number, month: number): number {
		return new Date(year, month, 0).getDate();
	}

	private formatMinutesSeconds(seconds: number): string {
		const minutes = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${minutes}:${secs.toString().padStart(2, '0')}`;
	}

	private getHost(secrets: ISecrets): string {
		return validateBackslash(secrets.cdn.host);
	}
}

{"ignorePatterns": ["node_modules/", "dist/"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "env": {"node": true, "es2022": true}, "plugins": ["@typescript-eslint", "import-newlines", "@stylistic/ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-function-return-type": "error", "@typescript-eslint/no-unused-vars": "warn", "@stylistic/ts/indent": ["error", "tab"], "linebreak-style": ["error", "unix"], "semi": ["error", "always"], "quotes": ["error", "double"], "no-trailing-spaces": "error", "comma-dangle": ["error", "never"], "no-mixed-spaces-and-tabs": "error", "space-in-parens": ["error", "never"], "max-len": ["error", {"code": 120}], "max-lines": ["error", {"max": 1000, "skipBlankLines": true, "skipComments": true}], "max-depth": ["error", 4], "max-lines-per-function": ["error", {"max": 200, "skipBlankLines": true, "skipComments": true}], "max-params": ["error", 4], "no-console": ["warn", {"allow": ["info", "error"]}], "no-unexpected-multiline": "error", "no-unreachable": "warn", "no-irregular-whitespace": "error", "no-fallthrough": "error", "no-duplicate-case": "error", "no-duplicate-imports": "error", "no-await-in-loop": "error", "camelcase": "error", "no-else-return": "error", "no-var": "error", "no-inline-comments": "error", "no-constant-condition": "warn", "no-undef": "warn", "no-nested-ternary": "error", "no-useless-catch": "error", "no-useless-return": "error", "comma-spacing": ["error", {"before": false, "after": true}], "comma-style": ["error", "last"], "dot-location": ["error", "property"], "eol-last": ["error", "always"], "func-call-spacing": ["error", "never"], "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "keyword-spacing": ["error", {"before": true, "after": true}], "function-call-argument-newline": ["error", "consistent"], "max-statements-per-line": ["error", {"max": 1}], "no-multi-spaces": "error", "semi-style": ["error", "last"], "no-warning-comments": ["warn", {"terms": ["todo", "fixme"], "location": "start"}], "import-newlines/enforce": ["error", {"items": 1, "max-len": 120, "semi": true, "forceSingleLine": true}], "space-infix-ops": ["error", {"int32Hint": false}]}, "overrides": [{"files": ["*.ts"], "rules": {"array-bracket-spacing": ["error", "never"], "object-curly-spacing": ["error", "always"]}}, {"files": ["*.js"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "array-bracket-spacing": ["error", "never"], "object-curly-spacing": ["error", "always"]}}], "globals": {"jest": "readonly", "Express": "readonly", "describe": "readonly", "beforeAll": "readonly", "afterAll": "readonly", "it": "readonly", "expect": "readonly", "beforeEach": "readonly", "afterEach": "readonly", "NodeJS": "readonly"}}